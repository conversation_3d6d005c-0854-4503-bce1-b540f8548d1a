import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import PDFGenerator from '../PDFGenerator';
import { usePDFGenerator } from '@/hooks/usePDFGenerator';

// Mock الـ hooks والمكتبات الخارجية
vi.mock('@/hooks/usePDFGenerator');
vi.mock('@react-pdf/renderer', () => ({
  Document: ({ children }: any) => <div data-testid="pdf-document">{children}</div>,
  Page: ({ children }: any) => <div data-testid="pdf-page">{children}</div>,
  Text: ({ children }: any) => <span data-testid="pdf-text">{children}</span>,
  View: ({ children }: any) => <div data-testid="pdf-view">{children}</div>,
  StyleSheet: {
    create: (styles: any) => styles,
  },
  PDFDownloadLink: ({ children, fileName }: any) => {
    const mockProps = {
      blob: null,
      url: null,
      loading: false,
      error: null,
    };
    return (
      <div data-testid="pdf-download-link" data-filename={fileName}>
        {typeof children === 'function' ? children(mockProps) : children}
      </div>
    );
  },
  Font: {
    register: vi.fn(),
  },
}));

vi.mock('../ArabicFontLoader', () => ({
  getDefaultArabicFont: () => 'Amiri',
  initializeArabicFonts: vi.fn().mockResolvedValue(true),
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
}));

const mockUsePDFGenerator = usePDFGenerator as vi.MockedFunction<typeof usePDFGenerator>;

describe('PDFGenerator', () => {
  const defaultMockReturn = {
    fontState: {
      isLoading: false,
      isLoaded: true,
      error: undefined,
    },
    downloadStatus: 'idle' as const,
    isDownloading: false,
    startDownload: vi.fn(),
    completeDownload: vi.fn(),
    errorDownload: vi.fn(),
    resetDownload: vi.fn(),
    initializeFonts: vi.fn(),
    progress: 0,
    lastDownloadTime: undefined,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUsePDFGenerator.mockReturnValue(defaultMockReturn);
  });

  it('يعرض مؤشر التحميل عندما تكون الخطوط قيد التحميل', () => {
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      fontState: {
        isLoading: true,
        isLoaded: false,
        error: undefined,
      },
    });

    render(<PDFGenerator />);

    expect(screen.getByText('جاري تحميل الخطوط العربية...')).toBeInTheDocument();
    expect(screen.getByText('تحضير نظام PDF بدعم كامل للغة العربية')).toBeInTheDocument();
  });

  it('يعرض رسالة خطأ عند فشل تحميل الخطوط', () => {
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      fontState: {
        isLoading: false,
        isLoaded: false,
        error: 'فشل في تحميل الخطوط',
      },
    });

    render(<PDFGenerator />);

    expect(screen.getByText('تحذير: فشل في تحميل الخطوط')).toBeInTheDocument();
    expect(screen.getByText('سيتم استخدام الخطوط الافتراضية')).toBeInTheDocument();
  });

  it('يعرض زر التحميل عندما تكون الخطوط محملة', () => {
    render(<PDFGenerator />);

    expect(screen.getByText('تحميل السيرة الذاتية PDF')).toBeInTheDocument();
    expect(screen.getByText('ملف PDF احترافي بدعم كامل للغة العربية')).toBeInTheDocument();
  });

  it('يعرض حالة التحميل الصحيحة', () => {
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      downloadStatus: 'downloading',
      isDownloading: true,
    });

    render(<PDFGenerator />);

    expect(screen.getByText('جاري إنشاء PDF...')).toBeInTheDocument();
  });

  it('يعرض حالة النجاح بعد التحميل', () => {
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      downloadStatus: 'success',
    });

    render(<PDFGenerator />);

    expect(screen.getByText('تم التحميل بنجاح!')).toBeInTheDocument();
  });

  it('يعرض حالة الخطأ عند فشل التحميل', () => {
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      downloadStatus: 'error',
    });

    render(<PDFGenerator />);

    expect(screen.getByText('إعادة المحاولة')).toBeInTheDocument();
  });

  it('يطبق الكلاسات المخصصة بشكل صحيح', () => {
    const customClass = 'custom-pdf-generator';
    const { container } = render(<PDFGenerator className={customClass} />);

    expect(container.firstChild).toHaveClass(customClass);
  });

  it('يحتوي على اسم الملف الصحيح', () => {
    render(<PDFGenerator />);

    const downloadLink = screen.getByTestId('pdf-download-link');
    const fileName = downloadLink.getAttribute('data-filename');
    
    expect(fileName).toContain('حذيفه_الحذيفي_السيرة_الذاتية_');
    expect(fileName).toContain('.pdf');
  });

  it('يعطل الزر عندما تكون الخطوط غير محملة', () => {
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      fontState: {
        isLoading: false,
        isLoaded: false,
        error: undefined,
      },
    });

    render(<PDFGenerator />);

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('يعطل الزر أثناء التحميل', () => {
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      downloadStatus: 'downloading',
      isDownloading: true,
    });

    render(<PDFGenerator />);

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('يعرض معلومات إضافية عن النظام', () => {
    render(<PDFGenerator />);

    expect(screen.getByText('يتضمن: خط Amiri الأنيق • تنسيق RTL • تصميم احترافي')).toBeInTheDocument();
  });

  it('يستخدم البيانات المخصصة عند توفيرها', () => {
    const customData = {
      personalInfo: {
        name: 'اسم مخصص',
        title: 'مسمى وظيفي مخصص',
        email: '<EMAIL>',
        phone: '+123456789',
        location: 'موقع مخصص',
      },
      summary: 'ملخص مخصص',
      education: [],
      experience: [],
      projects: [],
      skills: {
        frontend: [],
        backend: [],
        database: [],
        tools: [],
        soft: [],
      },
      languages: [],
      courses: [],
    };

    render(<PDFGenerator data={customData} />);

    // التأكد من أن المكون يعمل مع البيانات المخصصة
    expect(screen.getByText('تحميل السيرة الذاتية PDF')).toBeInTheDocument();
  });
});

describe('PDFGenerator Integration', () => {
  it('يتكامل مع usePDFGenerator hook بشكل صحيح', () => {
    const mockHook = {
      ...defaultMockReturn,
      startDownload: vi.fn(),
      completeDownload: vi.fn(),
      errorDownload: vi.fn(),
    };

    mockUsePDFGenerator.mockReturnValue(mockHook);

    render(<PDFGenerator />);

    // التأكد من استدعاء الـ hook مع المعاملات الصحيحة
    expect(mockUsePDFGenerator).toHaveBeenCalledWith({
      autoLoadFonts: true,
      showToasts: true,
    });
  });

  it('يستجيب لتغييرات حالة الخطوط', async () => {
    const { rerender } = render(<PDFGenerator />);

    // محاكاة تحميل الخطوط
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      fontState: {
        isLoading: true,
        isLoaded: false,
        error: undefined,
      },
    });

    rerender(<PDFGenerator />);
    expect(screen.getByText('جاري تحميل الخطوط العربية...')).toBeInTheDocument();

    // محاكاة اكتمال تحميل الخطوط
    mockUsePDFGenerator.mockReturnValue({
      ...defaultMockReturn,
      fontState: {
        isLoading: false,
        isLoaded: true,
        error: undefined,
      },
    });

    rerender(<PDFGenerator />);
    expect(screen.getByText('تحميل السيرة الذاتية PDF')).toBeInTheDocument();
  });
});
