import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { initializeArabicFonts } from '@/components/ArabicFontLoader';
import type { DownloadStatus, FontLoadingState, CVData } from '@/types/pdf';

interface UsePDFGeneratorOptions {
  autoLoadFonts?: boolean;
  showToasts?: boolean;
  onSuccess?: (fileName: string) => void;
  onError?: (error: Error) => void;
}

interface UsePDFGeneratorReturn {
  // حالة الخطوط
  fontState: FontLoadingState;
  
  // حالة التحميل
  downloadStatus: DownloadStatus;
  isDownloading: boolean;
  
  // وظائف التحكم
  initializeFonts: () => Promise<boolean>;
  startDownload: () => void;
  completeDownload: (fileName?: string) => void;
  errorDownload: (error?: Error) => void;
  resetDownload: () => void;
  
  // معلومات إضافية
  progress: number;
  lastDownloadTime?: Date;
}

export const usePDFGenerator = (options: UsePDFGeneratorOptions = {}): UsePDFGeneratorReturn => {
  const {
    autoLoadFonts = true,
    showToasts = true,
    onSuccess,
    onError
  } = options;

  // حالة الخطوط
  const [fontState, setFontState] = useState<FontLoadingState>({
    isLoading: false,
    isLoaded: false,
    error: undefined
  });

  // حالة التحميل
  const [downloadStatus, setDownloadStatus] = useState<DownloadStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [lastDownloadTime, setLastDownloadTime] = useState<Date>();

  // تهيئة الخطوط
  const initializeFonts = useCallback(async (): Promise<boolean> => {
    if (fontState.isLoaded) return true;

    setFontState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const success = await initializeArabicFonts();
      
      if (success) {
        setFontState({
          isLoading: false,
          isLoaded: true,
          error: undefined
        });

        if (showToasts) {
          toast.success('تم تحميل الخطوط العربية بنجاح', {
            description: 'النظام جاهز لإنشاء ملفات PDF احترافية',
            duration: 3000,
          });
        }

        return true;
      } else {
        throw new Error('فشل في تحميل الخطوط العربية');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      
      setFontState({
        isLoading: false,
        isLoaded: false,
        error: errorMessage
      });

      if (showToasts) {
        toast.error('خطأ في تحميل الخطوط', {
          description: 'سيتم استخدام الخطوط الافتراضية',
          duration: 5000,
        });
      }

      if (onError) {
        onError(error instanceof Error ? error : new Error(errorMessage));
      }

      return false;
    }
  }, [fontState.isLoaded, showToasts, onError]);

  // بدء التحميل
  const startDownload = useCallback(() => {
    setDownloadStatus('downloading');
    setProgress(0);

    if (showToasts) {
      toast.loading('جاري إنشاء ملف PDF...', {
        id: 'pdf-download',
        description: 'يتم تحضير السيرة الذاتية بدعم كامل للغة العربية',
      });
    }

    // محاكاة تقدم التحميل
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + Math.random() * 20;
      });
    }, 200);

    return () => clearInterval(progressInterval);
  }, [showToasts]);

  // إكمال التحميل بنجاح
  const completeDownload = useCallback((fileName?: string) => {
    setDownloadStatus('success');
    setProgress(100);
    setLastDownloadTime(new Date());

    if (showToasts) {
      toast.success('تم تحميل السيرة الذاتية بنجاح!', {
        id: 'pdf-download',
        description: fileName ? `تم حفظ الملف: ${fileName}` : 'تم إنشاء ملف PDF احترافي بدعم كامل للغة العربية',
        duration: 5000,
      });
    }

    if (onSuccess) {
      onSuccess(fileName || 'السيرة الذاتية');
    }

    // إعادة تعيين الحالة بعد فترة
    setTimeout(() => {
      setDownloadStatus('idle');
      setProgress(0);
    }, 3000);
  }, [showToasts, onSuccess]);

  // خطأ في التحميل
  const errorDownload = useCallback((error?: Error) => {
    setDownloadStatus('error');
    setProgress(0);

    const errorMessage = error?.message || 'حدث خطأ أثناء إنشاء ملف PDF';

    if (showToasts) {
      toast.error('فشل في تحميل السيرة الذاتية', {
        id: 'pdf-download',
        description: errorMessage,
        duration: 5000,
      });
    }

    if (onError) {
      onError(error || new Error(errorMessage));
    }

    // إعادة تعيين الحالة بعد فترة
    setTimeout(() => {
      setDownloadStatus('idle');
    }, 3000);
  }, [showToasts, onError]);

  // إعادة تعيين حالة التحميل
  const resetDownload = useCallback(() => {
    setDownloadStatus('idle');
    setProgress(0);
    toast.dismiss('pdf-download');
  }, []);

  // تحميل الخطوط تلقائياً عند تحميل المكون
  useEffect(() => {
    if (autoLoadFonts && !fontState.isLoaded && !fontState.isLoading) {
      initializeFonts();
    }
  }, [autoLoadFonts, fontState.isLoaded, fontState.isLoading, initializeFonts]);

  return {
    // حالة الخطوط
    fontState,
    
    // حالة التحميل
    downloadStatus,
    isDownloading: downloadStatus === 'downloading',
    
    // وظائف التحكم
    initializeFonts,
    startDownload,
    completeDownload,
    errorDownload,
    resetDownload,
    
    // معلومات إضافية
    progress,
    lastDownloadTime,
  };
};

// Hook مساعد لبيانات السيرة الذاتية
export const useCVData = (): CVData => {
  return {
    personalInfo: {
      name: 'حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي',
      title: 'مهندس تقنية معلومات',
      email: '<EMAIL>',
      phone: '+967 777548421 / +967 718706242',
      location: 'عدن، اليمن',
      website: 'www.hodifatech.com'
    },
    summary: `طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: C++, C#, Html, Css, Js, Php, Sql كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على Sqlserver و Mysql وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضا معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.`,
    education: [
      {
        institution: 'جامعة عدن - كلية الهندسة',
        degree: 'بكالوريوس تقنية المعلومات',
        status: 'السنة الرابعة',
        year: '2021 - 2025'
      }
    ],
    experience: [
      {
        title: 'شريك في تحليل قواعد البيانات',
        company: 'معهد تقني',
        description: 'عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية',
        year: '2023'
      },
      {
        title: 'تاجر حر',
        company: 'العمل الحر',
        description: 'أعمل في مجال التجارة الحرة',
        year: '2022 - الحاضر'
      }
    ],
    projects: [
      {
        name: 'برنامج إدارة الحجز',
        description: 'بناء برنامج desktop حر وبسيط في عمليات الحجز وفكرة البرنامج شاملة لأي جانب يتطلب إدارة الحجز بشكل عام',
        tech: 'C#, Windows Forms'
      },
      {
        name: 'متجر إلكتروني - Laravel 11',
        description: 'بناء موقع تجاري حر بإستخدام بيئة التعامل Laravel11 وقد خصصت الموقع أن يكون متجر يلبي احتياجات الأطفال حديثي الولادة',
        tech: 'Laravel 11, PHP, MySQL'
      },
      {
        name: 'تصميم قواعد البيانات للمدارس',
        description: 'عملت كشريك في تحليل وتصميم قواعد بيانات لأحدى مدارس القرى اليمنية',
        tech: 'SQL Server, Database Design'
      }
    ],
    skills: {
      frontend: ['React 18', 'TypeScript', 'HTML5', 'CSS3', 'JavaScript ES6+', 'Tailwind CSS'],
      backend: ['PHP', 'Laravel', 'C++', 'C#', 'Node.js'],
      database: ['SQL Server', 'MySQL', 'SQL'],
      tools: ['Git', 'GitHub', 'Vite', 'ESLint', 'Figma'],
      soft: ['سريع التعلم', 'مهارة التواصل', 'العمل ضمن فريق', 'مواكبة التقنيات الجديدة']
    },
    languages: [
      { name: 'العربية', level: 'الأم' },
      { name: 'الإنجليزية', level: 'ممتاز' },
      { name: 'الفرنسية', level: 'متوسط' }
    ],
    courses: [
      'أساسيات البرمجة و C++ - منصة Programming Advices (2022-2024)',
      'تطوير تطبيقات سطح المكتب - منصة Programming Advices (2024)',
      'تطوير المواقع Frontend - منصة Alzero Web School',
      'تطوير المواقع Backend - أكاديمية الجيل العربي',
      'اللغة الإنجليزية - معهد أميدست الأمريكي (سنة كاملة)',
      'CCNA - سبتمبر 2023',
      'CPS - معهد أميديست الأمريكي'
    ]
  };
};

export default usePDFGenerator;
