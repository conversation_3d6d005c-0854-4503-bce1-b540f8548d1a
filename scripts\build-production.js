#!/usr/bin/env node

/**
 * Production Build Script
 * يقوم بإعداد وبناء المشروع للإنتاج مع التحقق من جميع المتطلبات
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 بدء عملية البناء للإنتاج...\n');

// 1. التحقق من ملف .env
console.log('1️⃣ التحقق من متغيرات البيئة...');
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ ملف .env غير موجود!');
  console.log('💡 انسخ .env.example إلى .env وأضف القيم الحقيقية');
  process.exit(1);
}

const envContent = fs.readFileSync(envPath, 'utf8');
const requiredVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_DATABASE_URL',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

const missingVars = requiredVars.filter(varName => 
  !envContent.includes(`${varName}=`) || 
  envContent.includes(`${varName}=your_`) ||
  envContent.includes(`${varName}=`)
);

if (missingVars.length > 0) {
  console.error('❌ متغيرات البيئة التالية مفقودة أو غير مكتملة:');
  missingVars.forEach(varName => console.error(`   - ${varName}`));
  console.log('💡 تأكد من إضافة جميع قيم Firebase الحقيقية في ملف .env');
  process.exit(1);
}

console.log('✅ متغيرات البيئة مكتملة');

// 2. التحقق من التبعيات
console.log('\n2️⃣ التحقق من التبعيات...');
try {
  execSync('npm list firebase', { stdio: 'ignore' });
  console.log('✅ Firebase مثبت');
} catch (error) {
  console.error('❌ Firebase غير مثبت');
  console.log('💡 قم بتشغيل: npm install firebase');
  process.exit(1);
}

// 3. تشغيل الاختبارات (إذا كانت متوفرة)
console.log('\n3️⃣ تشغيل الاختبارات...');
try {
  execSync('npm run test -- --passWithNoTests', { stdio: 'inherit' });
  console.log('✅ الاختبارات نجحت');
} catch (error) {
  console.log('⚠️ لا توجد اختبارات أو فشلت - المتابعة...');
}

// 4. تنظيف مجلد البناء السابق
console.log('\n4️⃣ تنظيف البناء السابق...');
const distPath = path.join(process.cwd(), 'dist');
if (fs.existsSync(distPath)) {
  fs.rmSync(distPath, { recursive: true, force: true });
  console.log('✅ تم تنظيف مجلد dist');
}

// 5. بناء المشروع
console.log('\n5️⃣ بناء المشروع...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ تم بناء المشروع بنجاح');
} catch (error) {
  console.error('❌ فشل في بناء المشروع');
  process.exit(1);
}

// 6. التحقق من حجم الملفات
console.log('\n6️⃣ التحقق من حجم الملفات...');
const distSize = getDirSize(distPath);
const sizeMB = (distSize / 1024 / 1024).toFixed(2);
console.log(`📦 حجم البناء: ${sizeMB} MB`);

if (distSize > 50 * 1024 * 1024) { // 50MB
  console.warn('⚠️ حجم البناء كبير - قد يؤثر على سرعة التحميل');
}

// 7. إنشاء ملف معلومات البناء
console.log('\n7️⃣ إنشاء معلومات البناء...');
const buildInfo = {
  buildTime: new Date().toISOString(),
  version: require('../package.json').version,
  environment: 'production',
  size: `${sizeMB} MB`,
  firebase: {
    configured: true,
    projectId: process.env.VITE_FIREBASE_PROJECT_ID || 'unknown'
  }
};

fs.writeFileSync(
  path.join(distPath, 'build-info.json'),
  JSON.stringify(buildInfo, null, 2)
);

console.log('✅ تم إنشاء معلومات البناء');

// 8. نصائح للنشر
console.log('\n🎉 تم البناء بنجاح!');
console.log('\n📋 خطوات النشر:');
console.log('1. ارفع محتويات مجلد dist إلى الخادم');
console.log('2. تأكد من إعداد Firebase Hosting (اختياري)');
console.log('3. اختبر الموقع في بيئة الإنتاج');
console.log('4. راقب الأداء والأخطاء');

console.log('\n🔗 روابط مفيدة:');
console.log('- Firebase Console: https://console.firebase.google.com');
console.log('- مراقبة الأداء: https://web.dev/measure/');

function getDirSize(dirPath) {
  let size = 0;
  const files = fs.readdirSync(dirPath);
  
  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      size += getDirSize(filePath);
    } else {
      size += stats.size;
    }
  }
  
  return size;
}
