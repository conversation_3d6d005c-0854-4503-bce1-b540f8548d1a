{"version": 2, "buildCommand": "npm install && npx vite build", "outputDirectory": "dist", "installCommand": "npm install", "devCommand": "npm run dev", "framework": "vite", "env": {"NODE_VERSION": "18"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}