import React, { useState } from 'react';
import { Download, FileText, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useCVData } from '@/hooks/usePDFGenerator';
import { generatePDFFileName } from '@/utils/pdfUtils';
import type { CVData } from '@/types/pdf';

interface DirectPDFDownloaderProps {
  className?: string;
  data?: CVData;
}

const DirectPDFDownloader: React.FC<DirectPDFDownloaderProps> = ({ 
  className = '', 
  data 
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [downloadStatus, setDownloadStatus] = useState<'idle' | 'generating' | 'success' | 'error'>('idle');
  
  const defaultCVData = useCVData();
  const cvData = data || defaultCVData;

  const generatePDFContent = (data: CVData): string => {
    return `
%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
  /Font <<
    /F1 5 0 R
  >>
>>
>>
endobj

4 0 obj
<<
/Length 2000
>>
stream
BT
/F1 24 Tf
50 750 Td
(${data.personalInfo.name}) Tj
0 -30 Td
/F1 16 Tf
(${data.personalInfo.title}) Tj
0 -40 Td
/F1 12 Tf
(البريد الإلكتروني: ${data.personalInfo.email}) Tj
0 -20 Td
(الهاتف: ${data.personalInfo.phone}) Tj
0 -20 Td
(الموقع: ${data.personalInfo.location}) Tj
0 -40 Td
/F1 14 Tf
(الملخص المهني:) Tj
0 -25 Td
/F1 10 Tf
(${data.summary.substring(0, 200)}...) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000074 00000 n 
0000000120 00000 n 
0000000274 00000 n 
0000002324 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
2404
%%EOF
    `;
  };

  const downloadPDFDirect = async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);
      setDownloadStatus('generating');

      toast.loading('جاري إنشاء ملف PDF...', {
        id: 'pdf-direct',
        description: 'يتم تحضير السيرة الذاتية',
      });

      // محاكاة وقت المعالجة
      await new Promise(resolve => setTimeout(resolve, 1500));

      // إنشاء محتوى PDF بسيط
      const pdfContent = generatePDFContent(cvData);
      
      // تحويل إلى Blob
      const blob = new Blob([pdfContent], { type: 'application/pdf' });
      
      // إنشاء رابط التحميل
      const url = URL.createObjectURL(blob);
      const fileName = generatePDFFileName('حذيفه_الحذيفي_السيرة_الذاتية');

      // إنشاء عنصر رابط للتحميل
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      // إضافة للصفحة وتفعيل التحميل
      document.body.appendChild(link);
      link.click();

      // تنظيف الموارد
      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }, 100);

      setDownloadStatus('success');
      toast.success('تم تحميل السيرة الذاتية بنجاح!', {
        id: 'pdf-direct',
        description: `تم حفظ الملف: ${fileName}`,
        duration: 5000,
      });

      // إعادة تعيين الحالة
      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);

    } catch (error) {
      console.error('خطأ في إنشاء PDF:', error);
      setDownloadStatus('error');
      
      toast.error('فشل في تحميل السيرة الذاتية', {
        id: 'pdf-direct',
        description: 'حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.',
        duration: 5000,
      });

      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);
    } finally {
      setIsGenerating(false);
    }
  };

  // تحميل نص بسيط كبديل
  const downloadTextVersion = () => {
    const textContent = `
السيرة الذاتية

${cvData.personalInfo.name}
${cvData.personalInfo.title}

معلومات التواصل:
البريد الإلكتروني: ${cvData.personalInfo.email}
الهاتف: ${cvData.personalInfo.phone}
الموقع: ${cvData.personalInfo.location}
${cvData.personalInfo.website ? `الموقع الإلكتروني: ${cvData.personalInfo.website}` : ''}

الملخص المهني:
${cvData.summary}

التعليم:
${cvData.education.map(edu => `• ${edu.institution} - ${edu.degree} (${edu.year})`).join('\n')}

الخبرة العملية:
${cvData.experience.map(exp => `• ${exp.title} في ${exp.company} (${exp.year})\n  ${exp.description}`).join('\n\n')}

المشاريع:
${cvData.projects.map(project => `• ${project.name}\n  ${project.description}\n  التقنيات: ${project.tech}`).join('\n\n')}

المهارات التقنية:
تطوير الواجهات الأمامية: ${cvData.skills.frontend.join(', ')}
تطوير الخلفية: ${cvData.skills.backend.join(', ')}
قواعد البيانات: ${cvData.skills.database.join(', ')}
الأدوات: ${cvData.skills.tools.join(', ')}

اللغات:
${cvData.languages.map(lang => `• ${lang.name}: ${lang.level}`).join('\n')}

الدورات والشهادات:
${cvData.courses.map(course => `• ${course}`).join('\n')}
    `;

    const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const fileName = `حذيفه_الحذيفي_السيرة_الذاتية_${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}.txt`;

    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast.success('تم تحميل النسخة النصية!', {
      description: 'تم حفظ السيرة الذاتية كملف نصي',
      duration: 3000,
    });
  };

  const getButtonContent = () => {
    switch (downloadStatus) {
      case 'generating':
        return (
          <>
            <Loader2 className="w-5 h-5 ml-2 animate-spin" />
            جاري إنشاء PDF...
          </>
        );
      case 'success':
        return (
          <>
            <CheckCircle className="w-5 h-5 ml-2 text-green-500" />
            تم التحميل بنجاح!
          </>
        );
      case 'error':
        return (
          <>
            <AlertCircle className="w-5 h-5 ml-2 text-red-500" />
            إعادة المحاولة
          </>
        );
      default:
        return (
          <>
            <Download className="w-5 h-5 ml-2" />
            تحميل السيرة الذاتية PDF
          </>
        );
    }
  };

  const getButtonStyles = () => {
    switch (downloadStatus) {
      case 'generating':
        return 'bg-blue-600 hover:bg-blue-700 animate-pulse cursor-wait';
      case 'success':
        return 'bg-green-600 hover:bg-green-700';
      case 'error':
        return 'bg-red-600 hover:bg-red-700';
      default:
        return 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700';
    }
  };

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      {/* الزر الرئيسي */}
      <Button
        onClick={downloadPDFDirect}
        disabled={isGenerating}
        className={`
          w-full text-black font-bold text-lg px-8 py-4 
          rounded-xl shadow-lg hover:shadow-xl 
          transition-all duration-300 transform hover:scale-105
          disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          ${getButtonStyles()}
        `}
      >
        {getButtonContent()}
      </Button>

      {/* زر بديل للنسخة النصية */}
      <Button
        onClick={downloadTextVersion}
        variant="outline"
        className="w-full border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-black"
        disabled={isGenerating}
      >
        <FileText className="w-4 h-4 ml-2" />
        تحميل نسخة نصية (TXT)
      </Button>

      {/* معلومات إضافية */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
          <FileText className="w-4 h-4" />
          <span>تحميل مباشر للسيرة الذاتية</span>
        </div>
        
        <div className="text-xs text-gray-500">
          <span>PDF مبسط أو نسخة نصية كاملة</span>
        </div>

        {/* مؤشر التقدم */}
        {isGenerating && (
          <div className="w-full max-w-xs">
            <div className="bg-gray-200 rounded-full h-1.5 overflow-hidden">
              <div className="bg-amber-500 h-1.5 rounded-full animate-pulse w-full"></div>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              جاري معالجة البيانات...
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DirectPDFDownloader;
