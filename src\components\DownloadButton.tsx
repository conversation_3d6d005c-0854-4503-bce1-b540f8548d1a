import React, { useState } from 'react';
import { Download, FileText, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface DownloadButtonProps {
  onDownload: () => Promise<void> | void;
  fileName?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children?: React.ReactNode;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  onDownload,
  fileName = 'السيرة الذاتية',
  className = '',
  variant = 'default',
  size = 'md',
  disabled = false,
  children
}) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadStatus, setDownloadStatus] = useState<'idle' | 'downloading' | 'success' | 'error'>('idle');

  const handleDownload = async () => {
    if (isDownloading || disabled) return;

    try {
      setIsDownloading(true);
      setDownloadStatus('downloading');
      
      toast.loading('جاري تحضير الملف...', {
        id: 'download-toast',
        description: 'يتم إنشاء ملف PDF بدعم كامل للغة العربية',
      });

      await onDownload();

      setDownloadStatus('success');
      toast.success('تم تحميل الملف بنجاح!', {
        id: 'download-toast',
        description: `تم تحميل ${fileName} بنجاح`,
        duration: 4000,
      });

      // إعادة تعيين الحالة بعد فترة
      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);

    } catch (error) {
      setDownloadStatus('error');
      console.error('خطأ في التحميل:', error);
      
      toast.error('فشل في تحميل الملف', {
        id: 'download-toast',
        description: 'حدث خطأ أثناء إنشاء الملف. يرجى المحاولة مرة أخرى.',
        duration: 5000,
      });

      // إعادة تعيين الحالة بعد فترة
      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);
    } finally {
      setIsDownloading(false);
    }
  };

  const getButtonContent = () => {
    switch (downloadStatus) {
      case 'downloading':
        return (
          <>
            <Loader2 className="w-4 h-4 ml-2 animate-spin" />
            جاري التحميل...
          </>
        );
      case 'success':
        return (
          <>
            <CheckCircle className="w-4 h-4 ml-2 text-green-500" />
            تم التحميل بنجاح
          </>
        );
      case 'error':
        return (
          <>
            <AlertCircle className="w-4 h-4 ml-2 text-red-500" />
            فشل التحميل
          </>
        );
      default:
        return children || (
          <>
            <Download className="w-4 h-4 ml-2" />
            تحميل السيرة الذاتية PDF
          </>
        );
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-8 py-4 text-lg';
      default:
        return 'px-6 py-3 text-base';
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'outline':
        return 'border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-black';
      case 'secondary':
        return 'bg-gray-600 hover:bg-gray-700 text-white';
      default:
        return 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-black';
    }
  };

  const getStatusClasses = () => {
    switch (downloadStatus) {
      case 'downloading':
        return 'animate-pulse cursor-wait';
      case 'success':
        return 'bg-green-500 hover:bg-green-600 text-white';
      case 'error':
        return 'bg-red-500 hover:bg-red-600 text-white';
      default:
        return '';
    }
  };

  return (
    <div className="flex flex-col items-center gap-2">
      <Button
        onClick={handleDownload}
        disabled={isDownloading || disabled}
        className={cn(
          'font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105',
          'disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',
          getSizeClasses(),
          getVariantClasses(),
          getStatusClasses(),
          className
        )}
      >
        {getButtonContent()}
      </Button>

      {/* معلومات إضافية */}
      <div className="flex items-center gap-2 text-xs text-gray-400">
        <FileText className="w-3 h-3" />
        <span>ملف PDF احترافي بدعم كامل للغة العربية</span>
      </div>

      {/* مؤشر التقدم */}
      {isDownloading && (
        <div className="w-full max-w-xs">
          <div className="bg-gray-200 rounded-full h-1.5 overflow-hidden">
            <div className="bg-amber-500 h-1.5 rounded-full animate-pulse"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DownloadButton;
