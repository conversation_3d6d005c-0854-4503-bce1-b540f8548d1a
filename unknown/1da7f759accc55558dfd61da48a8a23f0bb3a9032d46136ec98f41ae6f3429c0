// Vercel Serverless Function لجلب الأخبار التقنية
// متوافق مع Vercel Edge Runtime

export default async function handler(req, res) {
  // إعداد CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // التعامل مع preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // السماح فقط بـ GET requests
  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    // استخدام NewsAPI.org مع API key من environment variables
    const API_KEY = process.env.NEWS_API_KEY || '1660ff496c4247c3a7d49457501feb73';
    
    // جرب NewsAPI.org أولاً
    let apiUrl = `https://newsapi.org/v2/everything?q=technology+programming+software+development&language=en&sortBy=publishedAt&pageSize=20&apiKey=${API_KEY}`;
    
    console.log('Fetching from NewsAPI.org...');
    let response = await fetch(apiUrl);
    
    if (!response.ok) {
      console.log('NewsAPI.org failed, trying NewsData.io...');
      // إذا فشل NewsAPI، جرب NewsData.io
      const NEWSDATA_API_KEY = process.env.NEWSDATA_API_KEY || '1660ff496c4247c3a7d49457501feb73';
      apiUrl = `https://newsdata.io/api/1/news?apikey=${NEWSDATA_API_KEY}&language=en&category=technology&size=20`;
      response = await fetch(apiUrl);
    }

    if (!response.ok) {
      console.log('Both APIs failed, trying RSS feeds...');
      // إذا فشلت كل APIs، استخدم RSS feeds
      return await handleRSSFeeds(res);
    }

    const data = await response.json();
    
    // تحويل البيانات إلى تنسيق موحد
    let articles = [];
    
    if (data.articles) {
      // NewsAPI.org format
      articles = data.articles.map(article => ({
        article_id: article.url,
        title: article.title,
        description: article.description,
        content: article.content,
        link: article.url,
        image_url: article.urlToImage,
        source_id: article.source?.name || 'Unknown',
        category: ['technology'],
        pubDate: article.publishedAt
      }));
    } else if (data.results) {
      // NewsData.io format
      articles = data.results.map(article => ({
        article_id: article.article_id,
        title: article.title,
        description: article.description,
        content: article.content,
        link: article.link,
        image_url: article.image_url,
        source_id: article.source_id,
        category: article.category || ['technology'],
        pubDate: article.pubDate
      }));
    }

    // فلترة المقالات وإزالة المحتوى غير المناسب
    const filteredArticles = articles
      .filter(article => 
        article.title && 
        article.description && 
        article.link &&
        !article.title.toLowerCase().includes('[removed]') &&
        !article.description.toLowerCase().includes('[removed]')
      )
      .slice(0, 12); // أخذ أول 12 مقال

    console.log(`Successfully fetched ${filteredArticles.length} articles`);

    res.status(200).json({
      success: true,
      articles: filteredArticles,
      total: filteredArticles.length,
      source: 'live_api'
    });

  } catch (error) {
    console.error('Error fetching news:', error);
    
    // في حالة الخطأ، إرجاع أخبار احتياطية
    return await handleFallbackNews(res);
  }
}

// دالة للتعامل مع RSS feeds كبديل
async function handleRSSFeeds(res) {
  try {
    // يمكن إضافة RSS parser هنا
    console.log('RSS feeds not implemented yet, using fallback...');
    return await handleFallbackNews(res);
  } catch (error) {
    return await handleFallbackNews(res);
  }
}

// دالة للأخبار الاحتياطية
async function handleFallbackNews(res) {
  const fallbackNews = [
    {
      article_id: "fallback_1",
      title: "AI Revolution in Software Development",
      description: "Artificial Intelligence is transforming how developers write, test, and deploy code, making development faster and more efficient.",
      content: "The integration of AI tools in software development has reached a tipping point...",
      link: "https://github.com/trending",
      image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
      source_id: "Tech News",
      category: ["technology", "ai"],
      pubDate: new Date().toISOString()
    },
    {
      article_id: "fallback_2",
      title: "React 19 Brings Revolutionary Features",
      description: "The latest React version introduces server components, improved performance, and better developer experience.",
      content: "React 19 represents a major leap forward in frontend development...",
      link: "https://react.dev",
      image_url: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
      source_id: "React Blog",
      category: ["technology", "react"],
      pubDate: new Date(Date.now() - 86400000).toISOString() // yesterday
    },
    {
      article_id: "fallback_3",
      title: "Cloud Security in Modern Applications",
      description: "As cloud adoption grows, security becomes paramount for protecting sensitive data and applications.",
      content: "Modern cloud security requires a multi-layered approach...",
      link: "https://aws.amazon.com/security/",
      image_url: "https://images.unsplash.com/photo-1563986768609-322da13575f3?w=500&h=300&fit=crop",
      source_id: "Cloud Security",
      category: ["technology", "security"],
      pubDate: new Date(Date.now() - 172800000).toISOString() // 2 days ago
    }
  ];

  res.status(200).json({
    success: true,
    articles: fallbackNews,
    total: fallbackNews.length,
    source: 'fallback'
  });
}
