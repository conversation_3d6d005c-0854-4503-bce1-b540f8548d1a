import { GraduationCap, Briefcase, Award, Languages } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ArabicPDFDownloader from "@/components/ArabicPDFDownloader";

const About = () => {

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">السيرة</span> الذاتية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <div className="max-w-md mx-auto">
              <ArabicPDFDownloader />
            </div>
          </div>

          {/* Personal Info with Profile Photo */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <div className="flex flex-col lg:flex-row items-center gap-8">
                {/* Profile Photo */}
                <div className="relative w-48 h-48 mx-auto lg:mx-0 group">
                  {/* Animated Border */}
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-500/30 to-amber-600/30 rounded-full animate-spin-slow"></div>
                  <div className="absolute inset-2 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full transition-all duration-500"></div>

                  {/* Profile Image */}
                  <div className="absolute inset-4 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-500">
                    <img
                      src="profile-photo.jpg"
                      alt="حذيفه عبدالمعز الحذيفي - مهندس تقنية معلومات"
                      className="w-full h-full object-cover object-center transition-all duration-500 group-hover:brightness-110"
                      loading="eager"
                    />
                    {/* Overlay Effect */}
                    <div className="absolute inset-0 bg-gradient-to-t from-amber-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                </div>

                {/* Personal Information */}
                <div className="text-center lg:text-right flex-1">
                  <CardTitle className="text-2xl text-amber-400 mb-2">
                    حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
                  </CardTitle>
                  <p className="text-xl text-gray-300 mb-4">مهندس تقنية معلومات</p>

                  {/* Contact Information */}
                  <div className="space-y-2 text-gray-400">
                    <p className="flex items-center justify-center lg:justify-start gap-2">
                      <span>📧</span>
                      <span><EMAIL></span>
                    </p>
                    <p className="flex items-center justify-center lg:justify-start gap-2">
                      <span>📱</span>
                      <span> 777548421 967+ |  718706242 967+</span>
                    </p>
                    <p className="flex items-center justify-center lg:justify-start gap-2">
                      <span>📍</span>
                      <span>عدن، اليمن</span>
                    </p>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الملخص المهني
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-gray-300 leading-relaxed space-y-4">
                <p>
                  طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة شاملة في تطوير التطبيقات الحديثة باستخدام أحدث التقنيات. أتقن تطوير الواجهات الأمامية باستخدام <span className="text-amber-400 font-semibold">React 18 مع TypeScript</span>، وأستخدم أدوات البناء المتقدمة مثل <span className="text-amber-400 font-semibold">Vite</span> لتطوير سريع وفعال.
                </p>

                <p>
                  لدي خبرة عملية في بناء مواقع ويب متكاملة باستخدام <span className="text-amber-400 font-semibold">Tailwind CSS و ShadCN/UI</span> لتصميم واجهات مستخدم احترافية ومتجاوبة. أعمل حالياً على تطوير موقعي الشخصي الاحترافي <span className="text-amber-400 font-semibold">(www.hodifatech.com)</span> باستخدام React 18, TypeScript, Vite, Tailwind CSS, React Router, React Query، وأنظمة PDF متقدمة.
                </p>

                <p>
                  كما لدي خبرة في تطوير الخلفية باستخدام <span className="text-amber-400 font-semibold">Laravel 11 و PHP</span>، وخبرة في قواعد البيانات <span className="text-amber-400 font-semibold">SQL Server و MySQL</span>. أتقن استخدام <span className="text-amber-400 font-semibold">Git و GitHub</span> لإدارة المشاريع، وأستخدم <span className="text-amber-400 font-semibold">ESLint و Vitest</span> للجودة والاختبار.
                </p>

                <p>
                  لدي معرفة في هندسة وتحليل البرمجيات وأنماط التصميم (Design Patterns)، وأسعى دائماً لتعلم التقنيات الجديدة وتطوير مهاراتي العملية في مجال تطوير الويب الحديث.
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <GraduationCap className="w-5 h-5 ml-2" />
                  التعليم
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-200">جامعة عدن - كلية الهندسة</h3>
                    <p className="text-gray-400">طالب بكلاريوس – تقنية المعلومات</p>
                    <p className="text-gray-400">السنة الرابعة</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <Languages className="w-5 h-5 ml-2" />
                  اللغات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">العربية</span>
                    <span className="text-amber-400">الأم</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الإنجليزية</span>
                    <span className="text-amber-400">ممتاز</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الفرنسية</span>
                    <span className="text-amber-400">متوسط</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الخبرة العملية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">كما أنني أعمل في مجال التجارة الحرة.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المشاريع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">برنامج إدارة الحجز</h4>
                  <p className="text-gray-300">بناء برنامج desktop حر وبسيط في عمليات الحجز وفكرة البرنامج شاملة لأي جانب يتطلب إدارة الحجز بشكل عام.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">متجر إلكتروني - Laravel 11</h4>
                  <p className="text-gray-300">بناء موقع تجاري حر بإستخدام بيئة التعامل Laravel11 وقد خصصت الموقع أن يكون متجر يلبي احتياجات الأطفال حديثي الولادة حتى سن السنتين ويجري التطوير عليه إلى أن يكون الموقع أكثر مرونة لأي جانب تجاري محتمل.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">تصميم قواعد البيانات للمدارس</h4>
                  <p className="text-gray-300">عملت كشريك في تحليل وتصميم قواعد بيانات لأحدى مدارس القرى اليمنية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">نظام إدارة الفنادق</h4>
                  <p className="text-gray-300">تحليل وتصميم قاعدة بيانات لأحد الفنادق في عدن</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Award className="w-5 h-5 ml-2" />
                الدورات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">أساسيات البرمجة و C++</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2022-2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير تطبيقات سطح المكتب</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Frontend</h4>
                  <p className="text-gray-400">منصة Alzero Web School المصرية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Backend</h4>
                  <p className="text-gray-400">أكاديمية الجيل العربي - اليمن</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">اللغة الإنجليزية</h4>
                  <p className="text-gray-400">معهد أميدست الأمريكي - عدن (سنة كاملة)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CCNA</h4>
                  <p className="text-gray-400">سبتمبر 2023</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">React 18 و TypeScript</h4>
                  <p className="text-gray-400">التعلم الذاتي والممارسة العملية (2024-2025)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">Tailwind CSS و ShadCN/UI</h4>
                  <p className="text-gray-400">التطوير العملي (2024-2025)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">Vite و أدوات البناء الحديثة</h4>
                  <p className="text-gray-400">الممارسة العملية (2024-2025)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">React Query و إدارة البيانات</h4>
                  <p className="text-gray-400">التطبيق العملي (2024-2025)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">أنظمة PDF المتقدمة</h4>
                  <p className="text-gray-400">التطوير والتطبيق (2024-2025)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CPS</h4>
                  <p className="text-gray-400">معهد أميديست الأمريكي</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المهارات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-6">
                  <h4 className="font-bold text-xl text-amber-400 mb-4 border-b border-amber-500/30 pb-2">
                    💻 المهارات التقنية
                  </h4>

                  <div className="grid grid-cols-1 gap-4">
                    {/* Frontend Development */}
                    <div className="bg-gray-800/30 rounded-lg p-4 border border-amber-500/20">
                      <h5 className="font-semibold text-amber-300 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                        تطوير الواجهات الأمامية
                      </h5>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        React 18 • TypeScript • HTML5 • CSS3 • JavaScript ES6+ • ShadCN/UI • React Router DOM • Lucide React
                      </p>
                    </div>

                    {/* Styling & UI */}
                    <div className="bg-gray-800/30 rounded-lg p-4 border border-amber-500/20">
                      <h5 className="font-semibold text-amber-300 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                        التصميم ومكتبات UI
                      </h5>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        Tailwind CSS • CSS Modules • Responsive Design • Radix UI • ShadCN/UI
                      </p>
                    </div>

                    {/* State Management */}
                    <div className="bg-gray-800/30 rounded-lg p-4 border border-amber-500/20">
                      <h5 className="font-semibold text-amber-300 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                        إدارة الحالة والبيانات
                      </h5>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        React Query (TanStack Query) • React Hooks • Context API
                      </p>
                    </div>

                    {/* Build Tools */}
                    <div className="bg-gray-800/30 rounded-lg p-4 border border-amber-500/20">
                      <h5 className="font-semibold text-amber-300 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                        أدوات البناء والتطوير
                      </h5>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        Vite • ESLint • TypeScript • PostCSS • Autoprefixer • Node.js • npm • Git • GitHub • NPM Scripts
                      </p>
                    </div>

                    {/* Backend & Database */}
                    <div className="bg-gray-800/30 rounded-lg p-4 border border-amber-500/20">
                      <h5 className="font-semibold text-amber-300 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                        تطوير الخلفية وقواعد البيانات
                      </h5>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        PHP • Laravel 11 • C++ • C# • Node.js • SQL Server • MySQL • SQL
                      </p>
                    </div>

                    {/* Specialized Systems */}
                    <div className="bg-gray-800/30 rounded-lg p-4 border border-amber-500/20">
                      <h5 className="font-semibold text-amber-300 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                        أنظمة متخصصة
                      </h5>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        @react-pdf/renderer • jsPDF • pdf-lib • Vitest • Testing Library • Jest DOM
                      </p>
                    </div>

                    {/* Tools & Deployment */}
                    <div className="bg-gray-800/30 rounded-lg p-4 border border-amber-500/20">
                      <h5 className="font-semibold text-amber-300 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                        أدوات التصميم والنشر
                      </h5>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        Figma • Canva • Adobe Tools • GitHub Pages • CI/CD • Environment Variables • Microsoft Office Suite
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-bold text-xl text-amber-400 mb-4 border-b border-amber-500/30 pb-2">
                    🌟 المهارات الشخصية
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-gray-800/30 rounded-lg p-3 border border-amber-500/20 text-center">
                      <span className="text-gray-300 font-medium">⚡ سريع التعلم</span>
                    </div>
                    <div className="bg-gray-800/30 rounded-lg p-3 border border-amber-500/20 text-center">
                      <span className="text-gray-300 font-medium">💬 مهارة التواصل</span>
                    </div>
                    <div className="bg-gray-800/30 rounded-lg p-3 border border-amber-500/20 text-center">
                      <span className="text-gray-300 font-medium">🤝 العمل ضمن فريق</span>
                    </div>
                    <div className="bg-gray-800/30 rounded-lg p-3 border border-amber-500/20 text-center">
                      <span className="text-gray-300 font-medium">🚀 مواكبة التقنيات الجديدة</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default About;
