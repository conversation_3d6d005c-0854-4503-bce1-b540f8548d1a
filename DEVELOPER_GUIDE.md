# دليل المطور - نظام PDF العربي

## نظرة عامة

هذا دليل شامل للمطورين الذين يريدون فهم أو تطوير أو تخصيص نظام PDF العربي المتطور.

## هيكل المشروع

```
src/
├── components/
│   ├── pdf/
│   │   └── index.ts              # تصدير جميع مكونات PDF
│   ├── PDFGenerator.tsx          # المكون الرئيسي لتوليد PDF
│   ├── PDFPreview.tsx           # معاينة السيرة الذاتية
│   ├── DownloadButton.tsx       # زر التحميل المحسن
│   ├── ThemeSelector.tsx        # اختيار القوالب
│   └── ArabicFontLoader.tsx     # إدارة الخطوط العربية
├── hooks/
│   └── usePDFGenerator.ts       # Hook لإدارة حالة PDF
├── types/
│   └── pdf.ts                   # تعريفات TypeScript
├── styles/
│   └── pdfThemes.ts            # قوالب الألوان والأنماط
├── utils/
│   └── pdfUtils.ts             # أدوات مساعدة
└── pages/
    ├── About.tsx               # صفحة السيرة الذاتية
    └── PDFTest.tsx            # صفحة الاختبار
```

## المكونات الأساسية

### 1. PDFGenerator

المكون الرئيسي لإنشاء وتحميل ملفات PDF.

```tsx
import { PDFGenerator } from '@/components/pdf';

// الاستخدام الأساسي
<PDFGenerator />

// مع بيانات مخصصة
<PDFGenerator data={customCVData} />

// مع كلاس مخصص
<PDFGenerator className="w-full max-w-md" />
```

**المميزات:**
- تحميل تلقائي للخطوط العربية
- معالجة الأخطاء المتقدمة
- مؤشرات التقدم
- دعم القوالب المتعددة

### 2. PDFPreview

مكون معاينة السيرة الذاتية مع خيارات متعددة.

```tsx
import { PDFPreview } from '@/components/pdf';

<PDFPreview />
```

**المميزات:**
- معاينة مدمجة وبملء الشاشة
- زر تحميل متكامل
- معاينة تفاعلية

### 3. DownloadButton

زر تحميل محسن مع مؤشرات الحالة.

```tsx
import { DownloadButton } from '@/components/pdf';

<DownloadButton
  onDownload={handleDownload}
  fileName="السيرة_الذاتية"
  size="lg"
  variant="default"
/>
```

### 4. ThemeSelector

مكون اختيار القوالب مع معاينة مباشرة.

```tsx
import { ThemeSelector } from '@/components/pdf';

<ThemeSelector
  selectedTheme={currentTheme}
  onThemeChange={setTheme}
/>
```

## الـ Hooks

### usePDFGenerator

Hook شامل لإدارة حالة نظام PDF.

```tsx
import { usePDFGenerator } from '@/hooks/usePDFGenerator';

const {
  fontState,
  downloadStatus,
  isDownloading,
  startDownload,
  completeDownload,
  errorDownload,
  progress
} = usePDFGenerator({
  autoLoadFonts: true,
  showToasts: true,
  onSuccess: (fileName) => console.log('تم التحميل:', fileName),
  onError: (error) => console.error('خطأ:', error)
});
```

### useCVData

Hook للحصول على بيانات السيرة الذاتية الافتراضية.

```tsx
import { useCVData } from '@/hooks/usePDFGenerator';

const cvData = useCVData();
```

## القوالب والأنماط

### استخدام القوالب

```tsx
import { 
  allThemes, 
  defaultTheme, 
  getThemeByName,
  createPDFStylesFromTheme 
} from '@/styles/pdfThemes';

// الحصول على قالب
const theme = getThemeByName('professional-blue');

// إنشاء أنماط PDF
const styles = createPDFStylesFromTheme(theme);
```

### إنشاء قالب مخصص

```tsx
import { PDFTheme } from '@/styles/pdfThemes';

const customTheme: PDFTheme = {
  name: 'custom-theme',
  displayName: 'قالب مخصص',
  colors: {
    primary: '#your-color',
    secondary: '#your-color',
    text: '#your-color',
    background: '#ffffff',
    accent: '#your-color',
    border: '#your-color',
  },
  fonts: {
    primary: 'Amiri',
    secondary: 'Cairo',
    heading: 'Cairo',
  },
  spacing: {
    small: 8,
    medium: 16,
    large: 24,
  },
  borderRadius: 4,
  borderWidth: 1,
};
```

## إدارة الخطوط

### تحميل الخطوط

```tsx
import { initializeArabicFonts, arabicFontOptions } from '@/components/ArabicFontLoader';

// تحميل جميع الخطوط
await initializeArabicFonts();

// الحصول على قائمة الخطوط
console.log(arabicFontOptions);
```

### إضافة خط جديد

```tsx
import { Font } from '@react-pdf/renderer';

Font.register({
  family: 'خط_جديد',
  fonts: [
    {
      src: 'رابط_الخط.woff2',
      fontWeight: 'normal',
    },
    {
      src: 'رابط_الخط_العريض.woff2',
      fontWeight: 'bold',
    }
  ]
});
```

## الأدوات المساعدة

### التحقق من صحة البيانات

```tsx
import { validateCVData } from '@/utils/pdfUtils';

const validation = validateCVData(cvData);
if (!validation.isValid) {
  console.error('أخطاء:', validation.errors);
}
```

### تنسيق البيانات

```tsx
import { formatDataForPrint, generatePDFFileName } from '@/utils/pdfUtils';

const cleanData = formatDataForPrint(cvData);
const fileName = generatePDFFileName('حذيفه_الحذيفي');
```

### تصدير واستيراد البيانات

```tsx
import { 
  exportCVDataAsJSON, 
  importCVDataFromJSON,
  createDataBackup 
} from '@/utils/pdfUtils';

// تصدير
const jsonData = exportCVDataAsJSON(cvData);

// استيراد
const importedData = importCVDataFromJSON(jsonString);

// نسخة احتياطية
createDataBackup(cvData);
```

## التخصيص المتقدم

### إنشاء مكون PDF مخصص

```tsx
import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
import { getDefaultArabicFont } from '@/components/ArabicFontLoader';

const styles = StyleSheet.create({
  page: {
    fontFamily: getDefaultArabicFont(),
    direction: 'rtl',
    padding: 30,
  },
  title: {
    fontSize: 24,
    color: '#f59e0b',
    textAlign: 'center',
  }
});

const CustomPDFDocument = ({ data }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <Text style={styles.title}>{data.personalInfo.name}</Text>
      {/* محتوى مخصص */}
    </Page>
  </Document>
);
```

### إضافة قسم جديد للسيرة الذاتية

1. تحديث نوع `CVData` في `src/types/pdf.ts`
2. إضافة القسم في `useCVData` hook
3. تحديث مكون `CVDocument` في `PDFGenerator.tsx`
4. إضافة التحقق في `validateCVData`

## الاختبار

### تشغيل الاختبارات

```bash
# جميع الاختبارات
npm test

# اختبارات محددة
npm test PDFGenerator

# مع التغطية
npm run test:coverage

# واجهة الاختبار
npm run test:ui
```

### كتابة اختبارات جديدة

```tsx
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import PDFGenerator from '../PDFGenerator';

describe('PDFGenerator', () => {
  it('يعرض زر التحميل', () => {
    render(<PDFGenerator />);
    expect(screen.getByText('تحميل السيرة الذاتية PDF')).toBeInTheDocument();
  });
});
```

## الأداء والتحسين

### تحسين تحميل الخطوط

```tsx
// تحميل مسبق للخطوط
useEffect(() => {
  const preloadFonts = async () => {
    await initializeArabicFonts();
  };
  preloadFonts();
}, []);
```

### تحسين حجم الحزمة

```tsx
// استيراد انتقائي
import { PDFGenerator } from '@/components/pdf';

// بدلاً من
import * as PDFComponents from '@/components/pdf';
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **الخطوط لا تظهر بشكل صحيح**
   - تأكد من تحميل الخطوط قبل إنشاء PDF
   - تحقق من روابط الخطوط

2. **النص العربي يظهر مقلوب**
   - تأكد من استخدام `direction: 'rtl'`
   - استخدم `textAlign: 'right'`

3. **بطء في التحميل**
   - استخدم lazy loading للمكونات
   - قم بتحميل الخطوط مسبقاً

### تسجيل الأخطاء

```tsx
import { usePDFGenerator } from '@/hooks/usePDFGenerator';

const { fontState, downloadStatus } = usePDFGenerator({
  onError: (error) => {
    console.error('PDF Error:', error);
    // إرسال إلى خدمة تسجيل الأخطاء
  }
});
```

## النشر والإنتاج

### متغيرات البيئة

```env
# اختياري: رابط خطوط مخصص
VITE_CUSTOM_FONTS_URL=https://your-cdn.com/fonts/

# اختياري: تفعيل تسجيل الأخطاء
VITE_PDF_DEBUG=true
```

### تحسين الإنتاج

```tsx
// في vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'pdf-system': ['@react-pdf/renderer', 'pdf-lib']
        }
      }
    }
  }
});
```

## المساهمة

### إضافة ميزة جديدة

1. إنشاء فرع جديد
2. تطوير الميزة مع الاختبارات
3. تحديث التوثيق
4. إرسال Pull Request

### معايير الكود

- استخدام TypeScript
- اتباع معايير ESLint
- كتابة اختبارات شاملة
- توثيق الكود بالعربية

## الدعم

للحصول على المساعدة:
1. راجع هذا الدليل
2. تحقق من الاختبارات الموجودة
3. استخدم صفحة `/pdf-test` للتجريب
4. راجع ملف `PDF_SYSTEM_README.md`

---

**تاريخ آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0
