import React, { useState } from 'react';
import { pdf } from '@react-pdf/renderer';
import { Download, FileText, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { usePDFGenerator, useCVData } from '@/hooks/usePDFGenerator';
import { CVDocument } from './PDFGenerator';
import type { CVData } from '@/types/pdf';

interface EnhancedPDFDownloaderProps {
  className?: string;
  data?: CVData;
}

const EnhancedPDFDownloader: React.FC<EnhancedPDFDownloaderProps> = ({ 
  className = '', 
  data 
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [downloadStatus, setDownloadStatus] = useState<'idle' | 'generating' | 'success' | 'error'>('idle');
  
  const {
    fontState,
    initializeFonts,
  } = usePDFGenerator({
    autoLoadFonts: true,
    showToasts: false, // سنتعامل مع الرسائل يدوياً
  });

  const defaultCVData = useCVData();
  const cvData = data || defaultCVData;

  const generateAndDownloadPDF = async () => {
    if (isGenerating || !fontState.isLoaded) return;

    try {
      setIsGenerating(true);
      setDownloadStatus('generating');

      toast.loading('جاري إنشاء ملف PDF...', {
        id: 'pdf-generation',
        description: 'يتم تحضير السيرة الذاتية بدعم كامل للغة العربية',
      });

      // إنشاء PDF باستخدام pdf() function
      const pdfBlob = await pdf(<CVDocument />).toBlob();

      // إنشاء رابط التحميل
      const url = URL.createObjectURL(pdfBlob);
      const fileName = `حذيفه_الحذيفي_السيرة_الذاتية_${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}.pdf`;

      // إنشاء عنصر رابط مؤقت للتحميل
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      // إضافة الرابط للصفحة وتفعيله
      document.body.appendChild(link);
      link.click();

      // تنظيف الموارد
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setDownloadStatus('success');
      toast.success('تم تحميل السيرة الذاتية بنجاح!', {
        id: 'pdf-generation',
        description: `تم حفظ الملف: ${fileName}`,
        duration: 5000,
      });

      // إعادة تعيين الحالة بعد 3 ثوان
      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);

    } catch (error) {
      console.error('خطأ في إنشاء PDF:', error);
      setDownloadStatus('error');
      
      toast.error('فشل في تحميل السيرة الذاتية', {
        id: 'pdf-generation',
        description: 'حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.',
        duration: 5000,
      });

      // إعادة تعيين الحالة بعد 3 ثوان
      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);
    } finally {
      setIsGenerating(false);
    }
  };

  // إذا لم يتم تحميل الخطوط بعد
  if (fontState.isLoading || !fontState.isLoaded) {
    return (
      <div className={`flex flex-col items-center gap-4 ${className}`}>
        <Button
          disabled
          className="w-full bg-gray-600 text-gray-300 font-bold text-lg px-8 py-4 rounded-xl"
        >
          <Loader2 className="w-5 h-5 ml-2 animate-spin" />
          {fontState.isLoading ? 'جاري تحميل الخطوط العربية...' : 'تحضير النظام...'}
        </Button>
        <div className="flex items-center gap-2 text-sm text-gray-400">
          <FileText className="w-4 h-4" />
          <span>تحضير نظام PDF بدعم كامل للغة العربية</span>
        </div>
        {fontState.error && (
          <div className="text-xs text-red-400 text-center">
            <p>تحذير: {fontState.error}</p>
            <p>سيتم استخدام الخطوط الافتراضية</p>
          </div>
        )}
      </div>
    );
  }

  const getButtonContent = () => {
    switch (downloadStatus) {
      case 'generating':
        return (
          <>
            <Loader2 className="w-5 h-5 ml-2 animate-spin" />
            جاري إنشاء PDF...
          </>
        );
      case 'success':
        return (
          <>
            <CheckCircle className="w-5 h-5 ml-2 text-green-500" />
            تم التحميل بنجاح!
          </>
        );
      case 'error':
        return (
          <>
            <AlertCircle className="w-5 h-5 ml-2 text-red-500" />
            إعادة المحاولة
          </>
        );
      default:
        return (
          <>
            <Download className="w-5 h-5 ml-2" />
            تحميل السيرة الذاتية PDF
          </>
        );
    }
  };

  const getButtonStyles = () => {
    switch (downloadStatus) {
      case 'generating':
        return 'bg-blue-600 hover:bg-blue-700 animate-pulse cursor-wait';
      case 'success':
        return 'bg-green-600 hover:bg-green-700';
      case 'error':
        return 'bg-red-600 hover:bg-red-700';
      default:
        return 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700';
    }
  };

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      <Button
        onClick={generateAndDownloadPDF}
        disabled={isGenerating || !fontState.isLoaded}
        className={`
          w-full text-black font-bold text-lg px-8 py-4 
          rounded-xl shadow-lg hover:shadow-xl 
          transition-all duration-300 transform hover:scale-105
          disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          ${getButtonStyles()}
        `}
      >
        {getButtonContent()}
      </Button>

      {/* معلومات إضافية */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
          <FileText className="w-4 h-4" />
          <span>ملف PDF احترافي بدعم كامل للغة العربية</span>
        </div>
        
        <div className="text-xs text-gray-500">
          <span>يتضمن: خط Amiri الأنيق • تنسيق RTL • تصميم احترافي</span>
        </div>

        {/* مؤشر التقدم أثناء التوليد */}
        {isGenerating && (
          <div className="w-full max-w-xs">
            <div className="bg-gray-200 rounded-full h-1.5 overflow-hidden">
              <div className="bg-amber-500 h-1.5 rounded-full animate-pulse w-full"></div>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              جاري معالجة الخطوط العربية وإنشاء PDF...
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedPDFDownloader;
