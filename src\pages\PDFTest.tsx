import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Download, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Eye,
  Palette,
  Type,
  Globe
} from 'lucide-react';
import PDFGenerator from '@/components/PDFGenerator';
import PDFPreview from '@/components/PDFPreview';
import DownloadButton from '@/components/DownloadButton';
import { usePDFGenerator } from '@/hooks/usePDFGenerator';
import { arabicFontOptions } from '@/components/ArabicFontLoader';

const PDFTest: React.FC = () => {
  const [selectedFont, setSelectedFont] = useState('amiri');
  const [testResults, setTestResults] = useState<{
    fontLoading: 'pending' | 'success' | 'error';
    pdfGeneration: 'pending' | 'success' | 'error';
    arabicSupport: 'pending' | 'success' | 'error';
  }>({
    fontLoading: 'pending',
    pdfGeneration: 'pending',
    arabicSupport: 'pending',
  });

  const {
    fontState,
    downloadStatus,
    isDownloading,
    initializeFonts,
    startDownload,
    completeDownload,
    errorDownload,
    progress,
  } = usePDFGenerator({
    autoLoadFonts: false,
    showToasts: true,
    onSuccess: () => {
      setTestResults(prev => ({ ...prev, pdfGeneration: 'success' }));
    },
    onError: () => {
      setTestResults(prev => ({ ...prev, pdfGeneration: 'error' }));
    },
  });

  const runFontTest = async () => {
    try {
      const success = await initializeFonts();
      setTestResults(prev => ({ 
        ...prev, 
        fontLoading: success ? 'success' : 'error',
        arabicSupport: success ? 'success' : 'error'
      }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        fontLoading: 'error',
        arabicSupport: 'error'
      }));
    }
  };

  const runPDFTest = async () => {
    try {
      startDownload();
      // محاكاة عملية إنشاء PDF
      await new Promise(resolve => setTimeout(resolve, 2000));
      completeDownload('test-file.pdf');
    } catch (error) {
      errorDownload(error as Error);
    }
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />;
    }
  };

  const getStatusText = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return 'نجح';
      case 'error':
        return 'فشل';
      default:
        return 'في الانتظار';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">
            <span className="text-amber-400">اختبار نظام</span> PDF
          </h1>
          <p className="text-gray-300">
            صفحة اختبار شاملة لنظام تحميل PDF بدعم اللغة العربية
          </p>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800">
            <TabsTrigger value="overview" className="data-[state=active]:bg-amber-500">
              <Eye className="w-4 h-4 ml-2" />
              نظرة عامة
            </TabsTrigger>
            <TabsTrigger value="tests" className="data-[state=active]:bg-amber-500">
              <Settings className="w-4 h-4 ml-2" />
              الاختبارات
            </TabsTrigger>
            <TabsTrigger value="fonts" className="data-[state=active]:bg-amber-500">
              <Type className="w-4 h-4 ml-2" />
              الخطوط
            </TabsTrigger>
            <TabsTrigger value="demo" className="data-[state=active]:bg-amber-500">
              <FileText className="w-4 h-4 ml-2" />
              العرض التوضيحي
            </TabsTrigger>
          </TabsList>

          {/* نظرة عامة */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="bg-gray-900/50 border-amber-500/20">
                <CardHeader>
                  <CardTitle className="text-amber-400 flex items-center">
                    <Type className="w-5 h-5 ml-2" />
                    حالة الخطوط
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>التحميل:</span>
                      <Badge variant={fontState.isLoaded ? 'default' : 'secondary'}>
                        {fontState.isLoaded ? 'مكتمل' : 'غير مكتمل'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>الحالة:</span>
                      <Badge variant={fontState.isLoading ? 'secondary' : 'default'}>
                        {fontState.isLoading ? 'جاري التحميل' : 'جاهز'}
                      </Badge>
                    </div>
                    {fontState.error && (
                      <div className="text-red-400 text-sm">
                        خطأ: {fontState.error}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900/50 border-amber-500/20">
                <CardHeader>
                  <CardTitle className="text-amber-400 flex items-center">
                    <Download className="w-5 h-5 ml-2" />
                    حالة التحميل
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>الحالة:</span>
                      <Badge variant={downloadStatus === 'success' ? 'default' : 'secondary'}>
                        {downloadStatus === 'idle' ? 'في الانتظار' :
                         downloadStatus === 'downloading' ? 'جاري التحميل' :
                         downloadStatus === 'success' ? 'نجح' : 'فشل'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>التقدم:</span>
                      <span className="text-amber-400">{progress}%</span>
                    </div>
                    {isDownloading && (
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-amber-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900/50 border-amber-500/20">
                <CardHeader>
                  <CardTitle className="text-amber-400 flex items-center">
                    <Globe className="w-5 h-5 ml-2" />
                    دعم العربية
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-center">
                      <div className="text-2xl mb-2" dir="rtl">
                        مرحباً بكم في نظام PDF
                      </div>
                      <div className="text-sm text-gray-400">
                        اختبار عرض النص العربي
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* الاختبارات */}
          <TabsContent value="tests" className="space-y-6">
            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-amber-400">اختبارات النظام</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                    <div>
                      <h3 className="font-semibold">تحميل الخطوط</h3>
                      <p className="text-sm text-gray-400">اختبار تحميل الخطوط العربية</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResults.fontLoading)}
                      <span className="text-sm">{getStatusText(testResults.fontLoading)}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                    <div>
                      <h3 className="font-semibold">إنشاء PDF</h3>
                      <p className="text-sm text-gray-400">اختبار إنشاء ملف PDF</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResults.pdfGeneration)}
                      <span className="text-sm">{getStatusText(testResults.pdfGeneration)}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                    <div>
                      <h3 className="font-semibold">دعم العربية</h3>
                      <p className="text-sm text-gray-400">اختبار عرض النصوص العربية</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResults.arabicSupport)}
                      <span className="text-sm">{getStatusText(testResults.arabicSupport)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button onClick={runFontTest} className="bg-blue-600 hover:bg-blue-700">
                    اختبار الخطوط
                  </Button>
                  <Button onClick={runPDFTest} className="bg-green-600 hover:bg-green-700">
                    اختبار PDF
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* الخطوط */}
          <TabsContent value="fonts" className="space-y-6">
            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-amber-400">الخطوط المتاحة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(arabicFontOptions).map(([key, font]) => (
                    <div 
                      key={key}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedFont === key 
                          ? 'border-amber-500 bg-amber-500/10' 
                          : 'border-gray-600 hover:border-gray-500'
                      }`}
                      onClick={() => setSelectedFont(key)}
                    >
                      <h3 className="font-semibold text-lg mb-2">{font.name}</h3>
                      <p className="text-sm text-gray-400 mb-3">{font.description}</p>
                      <div className="text-xl" style={{ fontFamily: font.family }}>
                        مرحباً بكم في نظام PDF العربي
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* العرض التوضيحي */}
          <TabsContent value="demo" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-900/50 border-amber-500/20">
                <CardHeader>
                  <CardTitle className="text-amber-400">مولد PDF الأساسي</CardTitle>
                </CardHeader>
                <CardContent>
                  <PDFGenerator className="w-full" />
                </CardContent>
              </Card>

              <Card className="bg-gray-900/50 border-amber-500/20">
                <CardHeader>
                  <CardTitle className="text-amber-400">زر التحميل المخصص</CardTitle>
                </CardHeader>
                <CardContent>
                  <DownloadButton
                    onDownload={async () => {
                      await new Promise(resolve => setTimeout(resolve, 2000));
                    }}
                    fileName="اختبار_PDF"
                    size="lg"
                    className="w-full"
                  />
                </CardContent>
              </Card>
            </div>

            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-amber-400">معاينة كاملة</CardTitle>
              </CardHeader>
              <CardContent>
                <PDFPreview />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PDFTest;
