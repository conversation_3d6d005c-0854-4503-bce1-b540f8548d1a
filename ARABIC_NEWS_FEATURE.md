# ميزة الأخبار العربية - Arabic News Feature

## 📋 نظرة عامة

تم إضافة ميزة جديدة لعرض الأخبار باللغة العربية من الدول العربية المختلفة إلى جانب الأخبار التقنية الإنجليزية الموجودة.

## 🌟 الميزات الجديدة

### 1. **اختيار اللغة**
- **الإنجليزية**: أخبار تقنية عالمية من مصادر موثوقة
- **العربية**: أخبار من الدول العربية المختلفة

### 2. **الدول العربية المدعومة**
- 🇦🇪 **الإمارات العربية المتحدة** (ae)
- 🇸🇦 **المملكة العربية السعودية** (sa)
- 🇪🇬 **مصر** (eg)
- 🇲🇦 **المغرب** (ma)

### 3. **واجهة المستخدم المحسنة**
- أزرار تبديل اللغة مع أيقونات الأعلام
- اختيار الدولة العربية عند تحديد اللغة العربية
- عداد الأخبار مع مصدر البيانات
- تصميم responsive ومتجاوب

## 🔧 التحديثات التقنية

### 1. **Serverless Functions**

#### `api/news.js` (Vercel)
```javascript
// دعم معاملات اللغة والدولة
const { language = 'en', country = null } = req.query;

// استخدام endpoints مختلفة حسب اللغة
if (language === 'ar' && country) {
  apiUrl = `https://newsapi.org/v2/top-headlines?country=${country}&apiKey=${API_KEY}`;
} else {
  apiUrl = `https://newsapi.org/v2/everything?q=technology+programming&language=en&apiKey=${API_KEY}`;
}
```

#### `netlify/functions/news.js` (Netlify)
```javascript
// استخراج المعاملات من query string
const queryParams = new URLSearchParams(event.queryStringParameters || {});
const language = queryParams.get('language') || 'en';
const country = queryParams.get('country') || null;
```

### 2. **Frontend Components**

#### State Management
```typescript
const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'ar'>('en');
const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
```

#### React Query Integration
```typescript
const { data: news = [] } = useQuery<any[]>({
  queryKey: ['techNews', selectedLanguage, selectedCountry],
  queryFn: () => fetchTechNews(selectedLanguage, selectedCountry),
  // ...
});
```

## 📡 API Endpoints

### الأخبار الإنجليزية
```
GET /api/news?language=en
```

### الأخبار العربية
```
GET /api/news?language=ar&country=ae  // الإمارات
GET /api/news?language=ar&country=sa  // السعودية
GET /api/news?language=ar&country=eg  // مصر
GET /api/news?language=ar&country=ma  // المغرب
```

## 🎨 UI/UX Improvements

### 1. **أزرار اللغة**
- تصميم متجاوب مع الألوان المميزة
- أيقونات الأعلام للتمييز البصري
- حالات active/inactive واضحة

### 2. **اختيار الدولة**
- يظهر فقط عند اختيار اللغة العربية
- أزرار منفصلة لكل دولة مع علمها
- تحديث تلقائي للأخبار عند التغيير

### 3. **عداد الأخبار**
- عرض عدد الأخبار المحملة
- إظهار مصدر البيانات (الدولة المختارة)
- تحديث ديناميكي مع تغيير الاختيارات

## 🔄 Fallback System

### 1. **أخبار عربية ثابتة**
```javascript
const arabicNews = [
  {
    title: "الإمارات تطلق استراتيجية جديدة للذكاء الاصطناعي 2031",
    source_id: "وكالة أنباء الإمارات",
    category: ["تقنية", "ذكاء اصطناعي"]
  },
  // المزيد من الأخبار...
];
```

### 2. **معالجة الأخطاء**
- أخبار احتياطية مناسبة للغة المختارة
- رسائل خطأ واضحة باللغة العربية
- إعادة المحاولة التلقائية

## 🚀 كيفية الاستخدام

1. **افتح صفحة الأخبار التقنية**
2. **اختر اللغة المطلوبة:**
   - 🇺🇸 English للأخبار التقنية الإنجليزية
   - 🇸🇦 العربية للأخبار العربية
3. **إذا اخترت العربية، اختر الدولة:**
   - 🇦🇪 الإمارات العربية المتحدة
   - 🇸🇦 المملكة العربية السعودية
   - 🇪🇬 مصر
   - 🇲🇦 المغرب
4. **انتظر تحميل الأخبار وتصفحها**

## 🔧 متطلبات التشغيل

- NewsAPI.org API Key
- React 18+
- TanStack Query (React Query)
- Tailwind CSS
- Lucide React Icons

## 📝 ملاحظات مهمة

- الأخبار العربية تأتي من NewsAPI.org top-headlines endpoint
- الأخبار الإنجليزية تأتي من everything endpoint مع فلترة تقنية
- يتم عرض أخبار احتياطية في حالة فشل API
- التصميم متجاوب ويعمل على جميع الأجهزة

## 🎯 التحسينات المستقبلية

- إضافة المزيد من الدول العربية
- فلترة الأخبار حسب الفئة
- حفظ تفضيلات المستخدم
- إضافة ميزة البحث في الأخبار
- تحسين SEO للأخبار العربية
