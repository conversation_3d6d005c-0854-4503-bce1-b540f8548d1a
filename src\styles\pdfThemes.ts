// قوالب الألوان والأنماط لنظام PDF

export interface PDFTheme {
  name: string;
  displayName: string;
  colors: {
    primary: string;
    secondary: string;
    text: string;
    background: string;
    accent: string;
    border: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    heading: string;
  };
  spacing: {
    small: number;
    medium: number;
    large: number;
  };
  borderRadius: number;
  borderWidth: number;
}

// القالب الافتراضي - الذهبي الأنيق
export const defaultTheme: PDFTheme = {
  name: 'default',
  displayName: 'الذهبي الأنيق',
  colors: {
    primary: '#f59e0b',      // amber-500
    secondary: '#6b7280',    // gray-500
    text: '#374151',         // gray-700
    background: '#ffffff',   // white
    accent: '#fbbf24',       // amber-400
    border: '#e5e7eb',       // gray-200
  },
  fonts: {
    primary: '<PERSON><PERSON>',
    secondary: 'Cairo',
    heading: 'Cairo',
  },
  spacing: {
    small: 8,
    medium: 16,
    large: 24,
  },
  borderRadius: 4,
  borderWidth: 1,
};

// القالب الأزرق المهني
export const professionalBlueTheme: PDFTheme = {
  name: 'professional-blue',
  displayName: 'الأزرق المهني',
  colors: {
    primary: '#2563eb',      // blue-600
    secondary: '#64748b',    // slate-500
    text: '#1e293b',         // slate-800
    background: '#ffffff',   // white
    accent: '#3b82f6',       // blue-500
    border: '#e2e8f0',       // slate-200
  },
  fonts: {
    primary: 'Cairo',
    secondary: 'Tajawal',
    heading: 'Cairo',
  },
  spacing: {
    small: 6,
    medium: 12,
    large: 20,
  },
  borderRadius: 6,
  borderWidth: 1.5,
};

// القالب الأخضر الطبيعي
export const naturalGreenTheme: PDFTheme = {
  name: 'natural-green',
  displayName: 'الأخضر الطبيعي',
  colors: {
    primary: '#059669',      // emerald-600
    secondary: '#6b7280',    // gray-500
    text: '#374151',         // gray-700
    background: '#ffffff',   // white
    accent: '#10b981',       // emerald-500
    border: '#d1d5db',       // gray-300
  },
  fonts: {
    primary: 'Lateef',
    secondary: 'NotoSansArabic',
    heading: 'Cairo',
  },
  spacing: {
    small: 10,
    medium: 18,
    large: 28,
  },
  borderRadius: 8,
  borderWidth: 2,
};

// القالب الأرجواني الإبداعي
export const creativePurpleTheme: PDFTheme = {
  name: 'creative-purple',
  displayName: 'الأرجواني الإبداعي',
  colors: {
    primary: '#7c3aed',      // violet-600
    secondary: '#6b7280',    // gray-500
    text: '#374151',         // gray-700
    background: '#ffffff',   // white
    accent: '#8b5cf6',       // violet-500
    border: '#e5e7eb',       // gray-200
  },
  fonts: {
    primary: 'Tajawal',
    secondary: 'Cairo',
    heading: 'Amiri',
  },
  spacing: {
    small: 12,
    medium: 20,
    large: 32,
  },
  borderRadius: 12,
  borderWidth: 1,
};

// القالب الأحمر القوي
export const boldRedTheme: PDFTheme = {
  name: 'bold-red',
  displayName: 'الأحمر القوي',
  colors: {
    primary: '#dc2626',      // red-600
    secondary: '#6b7280',    // gray-500
    text: '#374151',         // gray-700
    background: '#ffffff',   // white
    accent: '#ef4444',       // red-500
    border: '#fecaca',       // red-200
  },
  fonts: {
    primary: 'Cairo',
    secondary: 'Amiri',
    heading: 'Cairo',
  },
  spacing: {
    small: 8,
    medium: 16,
    large: 24,
  },
  borderRadius: 4,
  borderWidth: 2,
};

// القالب الرمادي الكلاسيكي
export const classicGrayTheme: PDFTheme = {
  name: 'classic-gray',
  displayName: 'الرمادي الكلاسيكي',
  colors: {
    primary: '#374151',      // gray-700
    secondary: '#6b7280',    // gray-500
    text: '#1f2937',         // gray-800
    background: '#ffffff',   // white
    accent: '#4b5563',       // gray-600
    border: '#d1d5db',       // gray-300
  },
  fonts: {
    primary: 'NotoSansArabic',
    secondary: 'Cairo',
    heading: 'Amiri',
  },
  spacing: {
    small: 6,
    medium: 14,
    large: 22,
  },
  borderRadius: 2,
  borderWidth: 1,
};

// مجموعة جميع القوالب
export const allThemes: PDFTheme[] = [
  defaultTheme,
  professionalBlueTheme,
  naturalGreenTheme,
  creativePurpleTheme,
  boldRedTheme,
  classicGrayTheme,
];

// دالة للحصول على قالب بالاسم
export const getThemeByName = (name: string): PDFTheme => {
  return allThemes.find(theme => theme.name === name) || defaultTheme;
};

// دالة لإنشاء أنماط PDF من القالب
export const createPDFStylesFromTheme = (theme: PDFTheme) => {
  return {
    page: {
      flexDirection: 'column' as const,
      backgroundColor: theme.colors.background,
      padding: theme.spacing.large,
      fontFamily: theme.fonts.primary,
      fontSize: 12,
      lineHeight: 1.6,
      direction: 'rtl' as const,
    },
    header: {
      marginBottom: theme.spacing.large,
      textAlign: 'center' as const,
      borderBottom: `${theme.borderWidth}px solid ${theme.colors.primary}`,
      paddingBottom: theme.spacing.medium,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold' as const,
      color: theme.colors.primary,
      marginBottom: theme.spacing.small,
      fontFamily: theme.fonts.heading,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.secondary,
      marginBottom: theme.spacing.small,
    },
    section: {
      marginBottom: theme.spacing.large,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: 'bold' as const,
      color: theme.colors.primary,
      marginBottom: theme.spacing.medium,
      borderBottom: `${theme.borderWidth}px solid ${theme.colors.border}`,
      paddingBottom: theme.spacing.small,
      fontFamily: theme.fonts.heading,
    },
    text: {
      fontSize: 11,
      color: theme.colors.text,
      marginBottom: theme.spacing.small,
      textAlign: 'right' as const,
      lineHeight: 1.8,
    },
    contactInfo: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      marginBottom: theme.spacing.medium,
      flexWrap: 'wrap' as const,
    },
    contactItem: {
      fontSize: 10,
      color: theme.colors.secondary,
      marginBottom: theme.spacing.small,
    },
    skillsContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: theme.spacing.medium,
    },
    skillItem: {
      backgroundColor: `${theme.colors.primary}20`, // 20% opacity
      padding: `${theme.spacing.small / 2} ${theme.spacing.small}`,
      borderRadius: theme.borderRadius,
      fontSize: 9,
      color: theme.colors.primary,
      marginBottom: theme.spacing.small,
    },
    listItem: {
      fontSize: 10,
      color: theme.colors.text,
      marginBottom: theme.spacing.small,
      paddingRight: theme.spacing.medium,
      textAlign: 'right' as const,
    },
    experienceItem: {
      marginBottom: theme.spacing.medium,
      paddingRight: theme.spacing.medium,
      borderRight: `${theme.borderWidth * 2}px solid ${theme.colors.primary}`,
    },
    experienceTitle: {
      fontSize: 12,
      fontWeight: 'bold' as const,
      color: theme.colors.text,
      marginBottom: theme.spacing.small / 2,
    },
    experienceDate: {
      fontSize: 9,
      color: theme.colors.secondary,
      marginBottom: theme.spacing.small,
    },
    footer: {
      marginTop: theme.spacing.large,
      textAlign: 'center' as const,
      fontSize: 8,
      color: theme.colors.secondary,
      borderTop: `${theme.borderWidth}px solid ${theme.colors.border}`,
      paddingTop: theme.spacing.medium,
    },
  };
};

// دالة لمعاينة الألوان
export const getThemePreviewColors = (theme: PDFTheme) => {
  return {
    primary: theme.colors.primary,
    secondary: theme.colors.secondary,
    accent: theme.colors.accent,
    background: theme.colors.background,
  };
};

// دالة للتحقق من تباين الألوان
export const checkColorContrast = (foreground: string, background: string): number => {
  // تحويل الألوان إلى RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  // حساب اللمعان النسبي
  const getLuminance = (r: number, g: number, b: number) => {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };

  const fg = hexToRgb(foreground);
  const bg = hexToRgb(background);

  if (!fg || !bg) return 0;

  const fgLum = getLuminance(fg.r, fg.g, fg.b);
  const bgLum = getLuminance(bg.r, bg.g, bg.b);

  const brightest = Math.max(fgLum, bgLum);
  const darkest = Math.min(fgLum, bgLum);

  return (brightest + 0.05) / (darkest + 0.05);
};

// دالة للتحقق من إمكانية الوصول للقالب
export const validateThemeAccessibility = (theme: PDFTheme): {
  isAccessible: boolean;
  issues: string[];
} => {
  const issues: string[] = [];
  
  // التحقق من تباين النص الأساسي
  const textContrast = checkColorContrast(theme.colors.text, theme.colors.background);
  if (textContrast < 4.5) {
    issues.push('تباين النص الأساسي منخفض جداً');
  }

  // التحقق من تباين العنوان الرئيسي
  const titleContrast = checkColorContrast(theme.colors.primary, theme.colors.background);
  if (titleContrast < 3) {
    issues.push('تباين العنوان الرئيسي منخفض جداً');
  }

  // التحقق من تباين النص الثانوي
  const secondaryContrast = checkColorContrast(theme.colors.secondary, theme.colors.background);
  if (secondaryContrast < 3) {
    issues.push('تباين النص الثانوي منخفض جداً');
  }

  return {
    isAccessible: issues.length === 0,
    issues
  };
};

export default {
  allThemes,
  defaultTheme,
  getThemeByName,
  createPDFStylesFromTheme,
  getThemePreviewColors,
  checkColorContrast,
  validateThemeAccessibility,
};
