# 🤝 المساهمة في المشروع

نرحب بمساهماتكم في تطوير الموقع الشخصي لحذيفة عبدالمعز! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 قبل البدء

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- Git
- محرر نصوص (VS Code مُوصى به)

### إعداد البيئة المحلية
```bash
# استنساخ المستودع
git clone https://github.com/HA1234098765/hodifa-portfolio.git

# الانتقال إلى مجلد المشروع
cd hodifa-portfolio

# تثبيت التبعيات
npm install

# نسخ ملف البيئة
cp .env.example .env

# تشغيل الخادم المحلي
npm run dev
```

## 🔄 عملية المساهمة

### 1. إنشاء فرع جديد
```bash
git checkout -b feature/اسم-الميزة
# أو
git checkout -b fix/اسم-الإصلاح
```

### 2. إجراء التغييرات
- اتبع معايير الكود الموجودة
- أضف تعليقات توضيحية باللغة العربية
- تأكد من أن التصميم متجاوب
- اختبر التغييرات على متصفحات مختلفة

### 3. اختبار التغييرات
```bash
# تشغيل الاختبارات
npm run lint

# بناء المشروع
npm run build

# اختبار المعاينة
npm run preview
```

### 4. إرسال Pull Request
- اكتب وصفاً واضحاً للتغييرات
- أرفق لقطات شاشة إذا كانت التغييرات بصرية
- تأكد من أن جميع الاختبارات تمر بنجاح

## 📝 معايير الكود

### JavaScript/TypeScript
- استخدم TypeScript للأمان النوعي
- اتبع معايير ESLint المُعرّفة
- استخدم أسماء متغيرات وصفية
- أضف تعليقات للدوال المعقدة

### CSS/Styling
- استخدم Tailwind CSS للتصميم
- اتبع نظام التصميم الموجود
- تأكد من دعم RTL للنصوص العربية
- اختبر على أحجام شاشات مختلفة

### Git Commits
```bash
# أمثلة على رسائل الـ commits
git commit -m "feat: إضافة ميزة الأخبار التقنية"
git commit -m "fix: إصلاح مشكلة التنقل في الجوال"
git commit -m "docs: تحديث ملف README"
git commit -m "style: تحسين تصميم الصفحة الرئيسية"
```

## 🐛 الإبلاغ عن الأخطاء

عند العثور على خطأ، يرجى إنشاء Issue جديد مع:
- وصف واضح للمشكلة
- خطوات إعادة إنتاج الخطأ
- لقطات شاشة إذا أمكن
- معلومات البيئة (متصفح، نظام تشغيل)

## 💡 اقتراح ميزات جديدة

لاقتراح ميزة جديدة:
- تأكد من أنها لم تُقترح من قبل
- اشرح الفائدة من الميزة
- قدم مثالاً أو تصميماً أولياً إذا أمكن

## 📞 التواصل

- **البريد الإلكتروني:** <EMAIL>
- **GitHub Issues:** للمشاكل التقنية
- **LinkedIn:** للاستفسارات العامة

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت نفس ترخيص المشروع (MIT).

---

**شكراً لمساهمتكم في تطوير هذا المشروع! 🙏**
