import { useEffect, useRef } from 'react';

interface UseChatNotificationsProps {
  messages: any[];
  currentUserId: string;
  isWindowFocused?: boolean;
}

export const useChatNotifications = ({ 
  messages, 
  currentUserId, 
  isWindowFocused = true 
}: UseChatNotificationsProps) => {
  const previousMessageCount = useRef(0);
  const lastMessageId = useRef<string>('');

  useEffect(() => {
    // تحقق من وجود رسائل جديدة
    if (messages.length > previousMessageCount.current && previousMessageCount.current > 0) {
      const latestMessage = messages[messages.length - 1];
      
      // لا تظهر إشعار للرسائل الخاصة بالمستخدم الحالي
      if (latestMessage.userId !== currentUserId && latestMessage.id !== lastMessageId.current) {
        // إشعار صوتي بسيط
        playNotificationSound();
        
        // إشعار المتصفح (إذا كان مسموح)
        if (!isWindowFocused && 'Notification' in window && Notification.permission === 'granted') {
          new Notification(`رسالة جديدة من ${latestMessage.username}`, {
            body: latestMessage.message.substring(0, 100) + (latestMessage.message.length > 100 ? '...' : ''),
            icon: '/logo.ico',
            tag: 'chat-message',
            requireInteraction: false,
            silent: false
          });
        }
        
        // تحديث عنوان الصفحة
        if (!isWindowFocused) {
          document.title = `💬 رسالة جديدة - الدردشة الجماعية`;
        }
        
        lastMessageId.current = latestMessage.id;
      }
    }
    
    previousMessageCount.current = messages.length;
  }, [messages, currentUserId, isWindowFocused]);

  // دالة تشغيل الصوت
  const playNotificationSound = () => {
    try {
      // إنشاء صوت بسيط باستخدام Web Audio API
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
      console.log('Could not play notification sound:', error);
    }
  };

  // طلب إذن الإشعارات
  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      try {
        const permission = await Notification.requestPermission();
        return permission === 'granted';
      } catch (error) {
        console.log('Could not request notification permission:', error);
        return false;
      }
    }
    return Notification.permission === 'granted';
  };

  // إعادة تعيين عنوان الصفحة عند التركيز
  const resetPageTitle = () => {
    document.title = 'الدردشة الجماعية - موقع حذيفة الحذيفي';
  };

  return {
    requestNotificationPermission,
    resetPageTitle,
    playNotificationSound
  };
};
