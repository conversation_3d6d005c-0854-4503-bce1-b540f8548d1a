#!/usr/bin/env node

/**
 * Firebase Deployment Script
 * سكريبت نشر سريع على Firebase Hosting
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 بدء عملية النشر على Firebase Hosting...\n');

// 1. التحقق من وجود مجلد dist
const distPath = path.join(process.cwd(), 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ مجلد dist غير موجود!');
  console.log('💡 قم بتشغيل: npm run build:production');
  process.exit(1);
}

console.log('✅ مجلد dist موجود');

// 2. التحقق من Firebase CLI
try {
  execSync('firebase --version', { stdio: 'ignore' });
  console.log('✅ Firebase CLI مثبت');
} catch (error) {
  console.error('❌ Firebase CLI غير مثبت');
  console.log('💡 قم بتشغيل: npm install -g firebase-tools');
  process.exit(1);
}

// 3. التحقق من تسجيل الدخول
try {
  const result = execSync('firebase projects:list', { encoding: 'utf8' });
  if (result.includes('myprofilewebsitechatproject')) {
    console.log('✅ تم تسجيل الدخول ومشروع Firebase متاح');
  } else {
    console.log('⚠️ المشروع غير متاح، جاري المحاولة...');
  }
} catch (error) {
  console.log('⚠️ يجب تسجيل الدخول إلى Firebase');
  console.log('💡 قم بتشغيل: firebase login');
  
  try {
    console.log('🔐 جاري فتح صفحة تسجيل الدخول...');
    execSync('firebase login', { stdio: 'inherit' });
  } catch (loginError) {
    console.error('❌ فشل في تسجيل الدخول');
    process.exit(1);
  }
}

// 4. النشر
console.log('\n🚀 جاري النشر...');
try {
  execSync('firebase deploy --only hosting', { stdio: 'inherit' });
  console.log('\n🎉 تم النشر بنجاح!');
  
  // 5. عرض الروابط
  console.log('\n🔗 روابط الموقع:');
  console.log('📱 الرابط الرئيسي: https://myprofilewebsitechatproject.web.app');
  console.log('🌐 الرابط البديل: https://myprofilewebsitechatproject.firebaseapp.com');
  console.log('💬 الدردشة: https://myprofilewebsitechatproject.web.app/group-chat');
  console.log('🧪 النسخة التجريبية: https://myprofilewebsitechatproject.web.app/test-chat');
  
  console.log('\n📊 المراقبة:');
  console.log('🔥 Firebase Console: https://console.firebase.google.com/project/myprofilewebsitechatproject');
  console.log('📈 Analytics: https://console.firebase.google.com/project/myprofilewebsitechatproject/analytics');
  
  console.log('\n✅ النشر مكتمل! يمكنك الآن زيارة موقعك.');
  
} catch (error) {
  console.error('❌ فشل في النشر');
  console.error(error.message);
  process.exit(1);
}
