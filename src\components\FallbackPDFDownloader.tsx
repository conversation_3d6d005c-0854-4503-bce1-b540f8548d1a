import React, { useState } from 'react';
import { Download, FileText, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useCVData } from '@/hooks/usePDFGenerator';
import jsPDF from 'jspdf';
import type { CVData } from '@/types/pdf';

interface FallbackPDFDownloaderProps {
  className?: string;
  data?: CVData;
}

const FallbackPDFDownloader: React.FC<FallbackPDFDownloaderProps> = ({ 
  className = '', 
  data 
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [downloadStatus, setDownloadStatus] = useState<'idle' | 'generating' | 'success' | 'error'>('idle');
  
  const defaultCVData = useCVData();
  const cvData = data || defaultCVData;

  const generatePDFWithJsPDF = async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);
      setDownloadStatus('generating');

      toast.loading('جاري إنشاء ملف PDF...', {
        id: 'pdf-fallback',
        description: 'يتم تحضير السيرة الذاتية باستخدام jsPDF',
      });

      // إنشاء PDF جديد
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // إعداد الخط
      doc.setFont("helvetica");
      
      // العنوان الرئيسي
      doc.setFontSize(20);
      doc.setTextColor(245, 158, 11); // amber color
      doc.text(cvData.personalInfo.name, 105, 20, { align: 'center' });
      
      doc.setFontSize(14);
      doc.setTextColor(100, 100, 100);
      doc.text(cvData.personalInfo.title, 105, 30, { align: 'center' });
      
      // خط فاصل
      doc.setDrawColor(245, 158, 11);
      doc.setLineWidth(0.5);
      doc.line(20, 35, 190, 35);
      
      // معلومات التواصل
      doc.setFontSize(16);
      doc.setTextColor(245, 158, 11);
      doc.text('Contact Information', 20, 45);
      
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      let yPos = 55;
      
      doc.text(`Email: ${cvData.personalInfo.email}`, 20, yPos);
      yPos += 7;
      doc.text(`Phone: ${cvData.personalInfo.phone}`, 20, yPos);
      yPos += 7;
      doc.text(`Location: ${cvData.personalInfo.location}`, 20, yPos);
      yPos += 7;
      if (cvData.personalInfo.website) {
        doc.text(`Website: ${cvData.personalInfo.website}`, 20, yPos);
        yPos += 7;
      }
      
      // الملخص المهني
      yPos += 10;
      doc.setFontSize(16);
      doc.setTextColor(245, 158, 11);
      doc.text('Professional Summary', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0);
      
      // تقسيم النص الطويل
      const summaryLines = doc.splitTextToSize(cvData.summary, 170);
      doc.text(summaryLines, 20, yPos);
      yPos += summaryLines.length * 5 + 10;
      
      // التعليم
      if (yPos > 250) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(16);
      doc.setTextColor(245, 158, 11);
      doc.text('Education', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      
      cvData.education.forEach(edu => {
        if (yPos > 270) {
          doc.addPage();
          yPos = 20;
        }
        doc.text(`• ${edu.institution}`, 25, yPos);
        yPos += 6;
        doc.text(`  ${edu.degree} - ${edu.status}`, 25, yPos);
        yPos += 6;
        doc.text(`  ${edu.year}`, 25, yPos);
        yPos += 10;
      });
      
      // الخبرة العملية
      if (yPos > 230) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(16);
      doc.setTextColor(245, 158, 11);
      doc.text('Work Experience', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      
      cvData.experience.forEach(exp => {
        if (yPos > 250) {
          doc.addPage();
          yPos = 20;
        }
        doc.text(`• ${exp.title}`, 25, yPos);
        yPos += 6;
        doc.text(`  ${exp.company} - ${exp.year}`, 25, yPos);
        yPos += 6;
        const descLines = doc.splitTextToSize(exp.description, 160);
        doc.text(descLines, 25, yPos);
        yPos += descLines.length * 5 + 8;
      });
      
      // المشاريع
      if (yPos > 200) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(16);
      doc.setTextColor(245, 158, 11);
      doc.text('Projects', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      
      cvData.projects.forEach(project => {
        if (yPos > 240) {
          doc.addPage();
          yPos = 20;
        }
        doc.text(`• ${project.name}`, 25, yPos);
        yPos += 6;
        doc.text(`  Technologies: ${project.tech}`, 25, yPos);
        yPos += 6;
        const projLines = doc.splitTextToSize(project.description, 160);
        doc.text(projLines, 25, yPos);
        yPos += projLines.length * 5 + 8;
      });
      
      // المهارات
      if (yPos > 180) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(16);
      doc.setTextColor(245, 158, 11);
      doc.text('Technical Skills', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0);
      
      doc.text('Frontend:', 25, yPos);
      doc.text(cvData.skills.frontend.join(', '), 50, yPos);
      yPos += 7;
      
      doc.text('Backend:', 25, yPos);
      doc.text(cvData.skills.backend.join(', '), 50, yPos);
      yPos += 7;
      
      doc.text('Database:', 25, yPos);
      doc.text(cvData.skills.database.join(', '), 50, yPos);
      yPos += 7;
      
      doc.text('Tools:', 25, yPos);
      doc.text(cvData.skills.tools.join(', '), 50, yPos);
      yPos += 10;
      
      // اللغات
      doc.setFontSize(16);
      doc.setTextColor(245, 158, 11);
      doc.text('Languages', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      
      cvData.languages.forEach(lang => {
        doc.text(`• ${lang.name}: ${lang.level}`, 25, yPos);
        yPos += 6;
      });
      
      // تحميل الملف
      const fileName = `Hodifa_Alhodaifi_CV_${new Date().toLocaleDateString('en-US').replace(/\//g, '-')}.pdf`;
      doc.save(fileName);

      setDownloadStatus('success');
      toast.success('تم تحميل السيرة الذاتية بنجاح!', {
        id: 'pdf-fallback',
        description: `تم حفظ الملف: ${fileName}`,
        duration: 5000,
      });

      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);

    } catch (error) {
      console.error('خطأ في إنشاء PDF:', error);
      setDownloadStatus('error');
      
      toast.error('فشل في تحميل السيرة الذاتية', {
        id: 'pdf-fallback',
        description: 'حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.',
        duration: 5000,
      });

      setTimeout(() => {
        setDownloadStatus('idle');
      }, 3000);
    } finally {
      setIsGenerating(false);
    }
  };

  const getButtonContent = () => {
    switch (downloadStatus) {
      case 'generating':
        return (
          <>
            <Loader2 className="w-5 h-5 ml-2 animate-spin" />
            جاري إنشاء PDF...
          </>
        );
      case 'success':
        return (
          <>
            <CheckCircle className="w-5 h-5 ml-2 text-green-500" />
            تم التحميل بنجاح!
          </>
        );
      case 'error':
        return (
          <>
            <AlertCircle className="w-5 h-5 ml-2 text-red-500" />
            إعادة المحاولة
          </>
        );
      default:
        return (
          <>
            <Download className="w-5 h-5 ml-2" />
            تحميل PDF (jsPDF)
          </>
        );
    }
  };

  const getButtonStyles = () => {
    switch (downloadStatus) {
      case 'generating':
        return 'bg-blue-600 hover:bg-blue-700 animate-pulse cursor-wait';
      case 'success':
        return 'bg-green-600 hover:bg-green-700';
      case 'error':
        return 'bg-red-600 hover:bg-red-700';
      default:
        return 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700';
    }
  };

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      <Button
        onClick={generatePDFWithJsPDF}
        disabled={isGenerating}
        className={`
          w-full text-white font-bold text-lg px-8 py-4 
          rounded-xl shadow-lg hover:shadow-xl 
          transition-all duration-300 transform hover:scale-105
          disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          ${getButtonStyles()}
        `}
      >
        {getButtonContent()}
      </Button>

      {/* معلومات إضافية */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
          <FileText className="w-4 h-4" />
          <span>PDF باستخدام jsPDF (بديل موثوق)</span>
        </div>
        
        <div className="text-xs text-gray-500">
          <span>يعمل في جميع المتصفحات • تحميل مضمون</span>
        </div>

        {/* مؤشر التقدم */}
        {isGenerating && (
          <div className="w-full max-w-xs">
            <div className="bg-gray-200 rounded-full h-1.5 overflow-hidden">
              <div className="bg-purple-500 h-1.5 rounded-full animate-pulse w-full"></div>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              جاري معالجة البيانات وإنشاء PDF...
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FallbackPDFDownloader;
