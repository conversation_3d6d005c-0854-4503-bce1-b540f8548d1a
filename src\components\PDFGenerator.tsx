import React from 'react';
import { Document, Page, Text, View, StyleSheet, PDFDownloadLink } from '@react-pdf/renderer';
import { Download, FileText, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getDefaultArabicFont } from './ArabicFontLoader';
import DownloadButton from './DownloadButton';
import { usePDFGenerator, useCVData } from '@/hooks/usePDFGenerator';
import type { PDFGeneratorProps } from '@/types/pdf';

// أنماط PDF مع دعم RTL
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontFamily: getDefaultArabicFont(),
    fontSize: 12,
    lineHeight: 1.6,
    direction: 'rtl',
  },
  header: {
    marginBottom: 20,
    textAlign: 'center',
    borderBottom: '2px solid #f59e0b',
    paddingBottom: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#f59e0b',
    marginBottom: 8,
    fontFamily: 'Cairo',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 5,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#f59e0b',
    marginBottom: 10,
    borderBottom: '1px solid #e5e7eb',
    paddingBottom: 5,
    fontFamily: 'Cairo',
  },
  text: {
    fontSize: 11,
    color: '#374151',
    marginBottom: 8,
    textAlign: 'right',
    lineHeight: 1.8,
  },
  contactInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    flexWrap: 'wrap',
  },
  contactItem: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 5,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  skillItem: {
    backgroundColor: '#fef3c7',
    padding: '4 8',
    borderRadius: 4,
    fontSize: 9,
    color: '#92400e',
    marginBottom: 5,
  },
  listItem: {
    fontSize: 10,
    color: '#374151',
    marginBottom: 6,
    paddingRight: 15,
    textAlign: 'right',
  },
  experienceItem: {
    marginBottom: 15,
    paddingRight: 10,
    borderRight: '3px solid #f59e0b',
  },
  experienceTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  experienceDate: {
    fontSize: 9,
    color: '#6b7280',
    marginBottom: 6,
  },
  footer: {
    marginTop: 30,
    textAlign: 'center',
    fontSize: 8,
    color: '#9ca3af',
    borderTop: '1px solid #e5e7eb',
    paddingTop: 10,
  }
});

// بيانات السيرة الذاتية
const cvData = {
  personalInfo: {
    name: 'حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي',
    title: 'مهندس تقنية معلومات',
    email: '<EMAIL>',
    phone: '+967 777548421 / +967 718706242',
    location: 'عدن، اليمن',
    website: 'www.hodifatech.com'
  },
  summary: `طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: C++, C#, Html, Css, Js, Php, Sql كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على Sqlserver و Mysql وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضا معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.`,
  education: [
    {
      institution: 'جامعة عدن - كلية الهندسة',
      degree: 'بكالوريوس تقنية المعلومات',
      status: 'السنة الرابعة',
      year: '2021 - 2025'
    }
  ],
  experience: [
    {
      title: 'شريك في تحليل قواعد البيانات',
      company: 'معهد تقني',
      description: 'عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية',
      year: '2023'
    },
    {
      title: 'تاجر حر',
      company: 'العمل الحر',
      description: 'أعمل في مجال التجارة الحرة',
      year: '2022 - الحاضر'
    }
  ],
  projects: [
    {
      name: 'برنامج إدارة الحجز',
      description: 'بناء برنامج desktop حر وبسيط في عمليات الحجز وفكرة البرنامج شاملة لأي جانب يتطلب إدارة الحجز بشكل عام',
      tech: 'C#, Windows Forms'
    },
    {
      name: 'متجر إلكتروني - Laravel 11',
      description: 'بناء موقع تجاري حر بإستخدام بيئة التعامل Laravel11 وقد خصصت الموقع أن يكون متجر يلبي احتياجات الأطفال حديثي الولادة',
      tech: 'Laravel 11, PHP, MySQL'
    },
    {
      name: 'تصميم قواعد البيانات للمدارس',
      description: 'عملت كشريك في تحليل وتصميم قواعد بيانات لأحدى مدارس القرى اليمنية',
      tech: 'SQL Server, Database Design'
    }
  ],
  skills: {
    frontend: ['React 18', 'TypeScript', 'HTML5', 'CSS3', 'JavaScript ES6+', 'Tailwind CSS'],
    backend: ['PHP', 'Laravel', 'C++', 'C#', 'Node.js'],
    database: ['SQL Server', 'MySQL', 'SQL'],
    tools: ['Git', 'GitHub', 'Vite', 'ESLint', 'Figma'],
    soft: ['سريع التعلم', 'مهارة التواصل', 'العمل ضمن فريق', 'مواكبة التقنيات الجديدة']
  },
  languages: [
    { name: 'العربية', level: 'الأم' },
    { name: 'الإنجليزية', level: 'ممتاز' },
    { name: 'الفرنسية', level: 'متوسط' }
  ],
  courses: [
    'أساسيات البرمجة و C++ - منصة Programming Advices (2022-2024)',
    'تطوير تطبيقات سطح المكتب - منصة Programming Advices (2024)',
    'تطوير المواقع Frontend - منصة Alzero Web School',
    'تطوير المواقع Backend - أكاديمية الجيل العربي',
    'اللغة الإنجليزية - معهد أميدست الأمريكي (سنة كاملة)',
    'CCNA - سبتمبر 2023',
    'CPS - معهد أميديست الأمريكي'
  ]
};

// مكون PDF Document
export const CVDocument: React.FC = () => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>{cvData.personalInfo.name}</Text>
        <Text style={styles.subtitle}>{cvData.personalInfo.title}</Text>
        <View style={styles.contactInfo}>
          <Text style={styles.contactItem}>📧 {cvData.personalInfo.email}</Text>
          <Text style={styles.contactItem}>📱 {cvData.personalInfo.phone}</Text>
          <Text style={styles.contactItem}>📍 {cvData.personalInfo.location}</Text>
          <Text style={styles.contactItem}>🌐 {cvData.personalInfo.website}</Text>
        </View>
      </View>

      {/* Professional Summary */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>الملخص المهني</Text>
        <Text style={styles.text}>{cvData.summary}</Text>
      </View>

      {/* Education */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>التعليم</Text>
        {cvData.education.map((edu, index) => (
          <View key={index} style={styles.experienceItem}>
            <Text style={styles.experienceTitle}>{edu.institution}</Text>
            <Text style={styles.text}>{edu.degree} - {edu.status}</Text>
            <Text style={styles.experienceDate}>{edu.year}</Text>
          </View>
        ))}
      </View>

      {/* Experience */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>الخبرة العملية</Text>
        {cvData.experience.map((exp, index) => (
          <View key={index} style={styles.experienceItem}>
            <Text style={styles.experienceTitle}>{exp.title}</Text>
            <Text style={styles.experienceDate}>{exp.company} - {exp.year}</Text>
            <Text style={styles.text}>{exp.description}</Text>
          </View>
        ))}
      </View>

      {/* Projects */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>المشاريع</Text>
        {cvData.projects.map((project, index) => (
          <View key={index} style={styles.experienceItem}>
            <Text style={styles.experienceTitle}>{project.name}</Text>
            <Text style={styles.experienceDate}>{project.tech}</Text>
            <Text style={styles.text}>{project.description}</Text>
          </View>
        ))}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text>تم إنشاء هذه السيرة الذاتية بواسطة نظام PDF احترافي يدعم اللغة العربية</Text>
        <Text>تاريخ الإنشاء: {new Date().toLocaleDateString('ar-EG')}</Text>
      </View>
    </Page>

    {/* الصفحة الثانية للمهارات والدورات */}
    <Page size="A4" style={styles.page}>
      {/* Skills */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>المهارات التقنية</Text>
        
        <View style={{ marginBottom: 15 }}>
          <Text style={styles.experienceTitle}>تطوير الواجهات الأمامية</Text>
          <View style={styles.skillsContainer}>
            {cvData.skills.frontend.map((skill, index) => (
              <Text key={index} style={styles.skillItem}>{skill}</Text>
            ))}
          </View>
        </View>

        <View style={{ marginBottom: 15 }}>
          <Text style={styles.experienceTitle}>تطوير الخلفية</Text>
          <View style={styles.skillsContainer}>
            {cvData.skills.backend.map((skill, index) => (
              <Text key={index} style={styles.skillItem}>{skill}</Text>
            ))}
          </View>
        </View>

        <View style={{ marginBottom: 15 }}>
          <Text style={styles.experienceTitle}>قواعد البيانات</Text>
          <View style={styles.skillsContainer}>
            {cvData.skills.database.map((skill, index) => (
              <Text key={index} style={styles.skillItem}>{skill}</Text>
            ))}
          </View>
        </View>

        <View style={{ marginBottom: 15 }}>
          <Text style={styles.experienceTitle}>الأدوات والتقنيات</Text>
          <View style={styles.skillsContainer}>
            {cvData.skills.tools.map((skill, index) => (
              <Text key={index} style={styles.skillItem}>{skill}</Text>
            ))}
          </View>
        </View>
      </View>

      {/* Languages */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>اللغات</Text>
        {cvData.languages.map((lang, index) => (
          <Text key={index} style={styles.listItem}>• {lang.name}: {lang.level}</Text>
        ))}
      </View>

      {/* Courses */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>الدورات والشهادات</Text>
        {cvData.courses.map((course, index) => (
          <Text key={index} style={styles.listItem}>• {course}</Text>
        ))}
      </View>

      {/* Soft Skills */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>المهارات الشخصية</Text>
        {cvData.skills.soft.map((skill, index) => (
          <Text key={index} style={styles.listItem}>• {skill}</Text>
        ))}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text>© 2025 حذيفه الحذيفي - جميع الحقوق محفوظة</Text>
        <Text>www.hodifatech.com</Text>
      </View>
    </Page>
  </Document>
);

const PDFGenerator: React.FC<PDFGeneratorProps> = ({ className = '', data }) => {
  // استخدام الـ hook المخصص لإدارة حالة PDF
  const {
    fontState,
    downloadStatus,
    isDownloading,
    startDownload,
    completeDownload,
    errorDownload,
  } = usePDFGenerator({
    autoLoadFonts: true,
    showToasts: true,
  });

  // استخدام بيانات السيرة الذاتية
  const defaultCVData = useCVData();
  const cvData = data || defaultCVData;

  // إذا لم يتم تحميل الخطوط بعد، عرض مؤشر التحميل
  if (fontState.isLoading || !fontState.isLoaded) {
    return (
      <div className={`flex flex-col items-center gap-4 ${className}`}>
        <Button
          disabled
          className="w-full bg-gray-600 text-gray-300 font-bold text-lg px-8 py-4 rounded-xl"
        >
          <Loader2 className="w-5 h-5 ml-2 animate-spin" />
          {fontState.isLoading ? 'جاري تحميل الخطوط العربية...' : 'تحضير النظام...'}
        </Button>
        <div className="flex items-center gap-2 text-sm text-gray-400">
          <FileText className="w-4 h-4" />
          <span>تحضير نظام PDF بدعم كامل للغة العربية</span>
        </div>
        {fontState.error && (
          <div className="text-xs text-red-400 text-center">
            <p>تحذير: {fontState.error}</p>
            <p>سيتم استخدام الخطوط الافتراضية</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      {/* الزر الرئيسي المحسن */}
      <PDFDownloadLink
        document={<CVDocument />}
        fileName={`حذيفه_الحذيفي_السيرة_الذاتية_${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}.pdf`}
        className="w-full"
      >
        {({ blob, url, loading, error }) => (
          <Button
            className={`
              w-full bg-gradient-to-r from-amber-500 to-amber-600
              hover:from-amber-600 hover:to-amber-700
              text-black font-bold text-lg px-8 py-4
              rounded-xl shadow-lg hover:shadow-xl
              transition-all duration-300 transform hover:scale-105
              disabled:opacity-50 disabled:cursor-not-allowed
              ${loading ? 'animate-pulse' : ''}
            `}
            disabled={loading || !fontState.isLoaded}
            onClick={() => {
              if (loading) return;
              startDownload();

              // إذا كان هناك URL جاهز، قم بالتحميل مباشرة
              if (url && !loading && !error) {
                const link = document.createElement('a');
                link.href = url;
                link.download = `حذيفه_الحذيفي_السيرة_الذاتية_${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}.pdf`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                completeDownload(link.download);
              }
            }}
          >
            {loading || isDownloading ? (
              <>
                <Loader2 className="w-5 h-5 ml-2 animate-spin" />
                جاري إنشاء PDF...
              </>
            ) : downloadStatus === 'success' ? (
              <>
                <Download className="w-5 h-5 ml-2" />
                تم التحميل بنجاح!
              </>
            ) : downloadStatus === 'error' || error ? (
              <>
                <Download className="w-5 h-5 ml-2" />
                إعادة المحاولة
              </>
            ) : (
              <>
                <Download className="w-5 h-5 ml-2" />
                تحميل السيرة الذاتية PDF
              </>
            )}
          </Button>
        )}
      </PDFDownloadLink>

      {/* معلومات إضافية */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
          <FileText className="w-4 h-4" />
          <span>ملف PDF احترافي بدعم كامل للغة العربية</span>
        </div>

        <div className="text-xs text-gray-500">
          <span>يتضمن: خط Amiri الأنيق • تنسيق RTL • تصميم احترافي</span>
        </div>
      </div>
    </div>
  );
};

export default PDFGenerator;
