import { Font } from '@react-pdf/renderer';

// تحميل الخطوط العربية المختلفة
export const loadArabicFonts = () => {
  // خط Amiri - خط عربي تقليدي أنيق
  Font.register({
    family: '<PERSON>i',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/amiri/v27/J7aRnpd8CGxBHqUpvrIw74NL.woff2',
        fontWeight: 'normal',
      },
      {
        src: 'https://fonts.gstatic.com/s/amiri/v27/J7afnpd8CGxBHqUpvrIw-xNLxQs.woff2',
        fontWeight: 'bold',
      }
    ]
  });

  // خط Cairo - خط عربي حديث
  Font.register({
    family: 'Cairo',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8gfxJkAg.woff2',
        fontWeight: 'normal',
      },
      {
        src: 'https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8gfxJkAg.woff2',
        fontWeight: 'bold',
      }
    ]
  });

  // خط Tajawal - خط عربي عصري
  Font.register({
    family: 'Tajawal',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2',
        fontWeight: 'normal',
      },
      {
        src: 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2',
        fontWeight: 'bold',
      }
    ]
  });

  // خط Lateef - خط عربي للنصوص الطويلة
  Font.register({
    family: 'Lateef',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/lateef/v24/hESw6XVnNCxEvkbMpheEZo_H_w.woff2',
        fontWeight: 'normal',
      }
    ]
  });

  // خط Noto Sans Arabic - خط Google للعربية
  Font.register({
    family: 'NotoSansArabic',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu3CBFQLaig.woff2',
        fontWeight: 'normal',
      },
      {
        src: 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu3CBFQLaig.woff2',
        fontWeight: 'bold',
      }
    ]
  });
};

// خيارات الخطوط المتاحة
export const arabicFontOptions = {
  amiri: {
    name: 'Amiri',
    description: 'خط عربي تقليدي أنيق مناسب للنصوص الرسمية',
    family: 'Amiri'
  },
  cairo: {
    name: 'Cairo',
    description: 'خط عربي حديث وواضح مناسب للاستخدام العام',
    family: 'Cairo'
  },
  tajawal: {
    name: 'Tajawal',
    description: 'خط عربي عصري ومقروء',
    family: 'Tajawal'
  },
  lateef: {
    name: 'Lateef',
    description: 'خط عربي مناسب للنصوص الطويلة',
    family: 'Lateef'
  },
  notoSansArabic: {
    name: 'Noto Sans Arabic',
    description: 'خط Google العربي الموثوق',
    family: 'NotoSansArabic'
  }
};

// دالة للحصول على خط افتراضي
export const getDefaultArabicFont = () => arabicFontOptions.amiri.family;

// دالة للتحقق من تحميل الخطوط
export const checkFontLoading = async (fontFamily: string): Promise<boolean> => {
  try {
    // محاولة إنشاء عنصر نص بالخط المحدد
    const testElement = document.createElement('div');
    testElement.style.fontFamily = fontFamily;
    testElement.style.position = 'absolute';
    testElement.style.visibility = 'hidden';
    testElement.textContent = 'اختبار الخط العربي';
    
    document.body.appendChild(testElement);
    
    // التحقق من تحميل الخط
    const computedStyle = window.getComputedStyle(testElement);
    const loadedFont = computedStyle.fontFamily;
    
    document.body.removeChild(testElement);
    
    return loadedFont.includes(fontFamily);
  } catch (error) {
    console.warn(`فشل في التحقق من تحميل الخط ${fontFamily}:`, error);
    return false;
  }
};

// دالة لتحميل خط احتياطي
export const loadFallbackFont = () => {
  Font.register({
    family: 'ArabicFallback',
    fonts: [
      {
        src: 'data:font/woff2;base64,', // يمكن إضافة خط احتياطي مدمج هنا
        fontWeight: 'normal',
      }
    ]
  });
};

// تهيئة الخطوط العربية
export const initializeArabicFonts = async () => {
  try {
    loadArabicFonts();
    console.log('تم تحميل الخطوط العربية بنجاح');
    return true;
  } catch (error) {
    console.error('خطأ في تحميل الخطوط العربية:', error);
    loadFallbackFont();
    return false;
  }
};

export default {
  loadArabicFonts,
  arabicFontOptions,
  getDefaultArabicFont,
  checkFontLoading,
  loadFallbackFont,
  initializeArabicFonts
};
