
import { ArrowDown, Download, <PERSON>, <PERSON>, Sun } from "lucide-react";
import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

const Index = () => {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black dark:from-black dark:via-gray-900 dark:to-black light:from-white light:via-gray-50 light:to-gray-100 text-white dark:text-white light:text-gray-900 transition-all duration-500">
      {/* Theme Toggle Button */}
      <div className="fixed top-6 right-6 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          className="bg-amber-500/20 border-amber-500/40 hover:bg-amber-500/30 transition-all duration-300 backdrop-blur-sm"
        >
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        </Button>
      </div>

      <Navigation />
      
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Enhanced Background Pattern with Animation */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-20 w-72 h-72 bg-amber-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-amber-600/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-amber-400/5 rounded-full blur-3xl animate-ping delay-2000"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-center lg:text-right animate-fade-in">
              <div className="mb-6">
                <div className="inline-block w-1 h-16 bg-amber-400 ml-4 animate-pulse"></div>
              </div>
              
              <h1 className="text-5xl lg:text-7xl font-bold mb-4 animate-scale-in">
                حذيفه عبدالمعز
              </h1>
              
              <p className="text-xl lg:text-2xl text-gray-300 dark:text-gray-300 light:text-gray-600 mb-8 animate-fade-in delay-300">
                مهندس تقنية معلومات / مطور ويب
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in delay-500">
                <Link to="/contact">
                  <Button className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-amber-500/25">
                    تواصل معي
                  </Button>
                </Link>
                
                <Link to="/projects" className="flex items-center space-x-2 space-x-reverse">
                  <Button variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-black px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105">
                    <Eye className="w-4 h-4 ml-2" />
                    عرض المشاريع
                  </Button>
                </Link>
              </div>
            </div>

            {/* Right Content - Enhanced Profile Image */}
            <div className="relative animate-fade-in delay-700">
              <div className="relative w-80 h-80 lg:w-96 lg:h-96 mx-auto group">
                {/* Animated Border */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/30 to-amber-600/30 rounded-full animate-spin-slow"></div>
                <div className="absolute inset-2 bg-gradient-to-br from-gray-800 to-gray-900 dark:from-gray-800 dark:to-gray-900 light:from-white light:to-gray-100 rounded-full transition-all duration-500"></div>
                
                {/* Profile Image */}
                <div className="absolute inset-4 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-500">
                  <img
                    src="profile-photo.jpg"
                    alt="حذيفه عبدالمعز الحذيفي - مهندس تقنية معلومات"
                    className="w-full h-full object-cover object-center transition-all duration-500 group-hover:brightness-110"
                    loading="eager"
                  />
                  {/* Overlay Effect */}
                  <div className="absolute inset-0 bg-gradient-to-t from-amber-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
              </div>
              
              {/* Enhanced Decorative Elements */}
              <div className="absolute top-10 right-10 w-4 h-4 bg-amber-400 rounded-full animate-ping"></div>
              <div className="absolute bottom-20 left-10 w-6 h-6 border-2 border-amber-400 rounded-full animate-pulse"></div>
              <div className="absolute top-1/2 left-0 w-2 h-20 bg-amber-400/50 animate-pulse delay-1000"></div>
              <div className="absolute top-1/4 right-5 w-3 h-3 bg-amber-300 rounded-full animate-bounce delay-2000"></div>
            </div>
          </div>

          {/* Enhanced Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="flex flex-col items-center space-y-2">
              <ArrowDown className="w-6 h-6 text-amber-400 animate-pulse" />
              <div className="w-px h-8 bg-gradient-to-b from-amber-400 to-transparent"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Quick Overview Section */}
      <section className="py-20 bg-gradient-to-br from-black/60 via-gray-900/50 to-black/60 backdrop-blur-sm transition-all duration-500 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-32 h-32 border border-amber-400 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 border border-amber-400 rounded-full animate-pulse delay-300"></div>
          <div className="absolute bottom-20 left-1/4 w-16 h-16 border border-amber-400 rounded-full animate-pulse delay-700"></div>
          <div className="absolute bottom-40 right-1/3 w-20 h-20 border border-amber-400 rounded-full animate-pulse delay-500"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20 animate-fade-in">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full mb-6 animate-bounce">
              <span className="text-2xl">💻</span>
            </div>
            <h2 className="text-5xl font-bold bg-gradient-to-r from-amber-400 via-amber-300 to-amber-500 bg-clip-text text-transparent mb-4">
              نبذة تعريفية
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent mx-auto animate-scale-in"></div>
            <p className="text-gray-300 mt-6 text-lg max-w-2xl mx-auto">
              مطور ويب شغوف بالتقنيات الحديثة ومتخصص في بناء تطبيقات ويب متطورة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {/* التعليم */}
            <div className="group relative bg-gradient-to-br from-gray-900/80 to-gray-800/60 p-8 rounded-2xl border border-amber-500/20 hover:border-amber-400/60 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-500/20 animate-fade-in delay-200 overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-amber-400/20 to-transparent rounded-bl-full"></div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-xl">🎓</span>
                </div>
                <h3 className="text-xl font-bold text-amber-400 mb-3 group-hover:text-amber-300 transition-colors">التعليم الأكاديمي</h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  بكالوريوس تقنية المعلومات - السنة الرابعة
                  <br />
                  <span className="text-amber-400 font-semibold">جامعة عدن - كلية الهندسة</span>
                </p>
              </div>
            </div>

            {/* التقنيات الحديثة */}
            <div className="group relative bg-gradient-to-br from-gray-900/80 to-gray-800/60 p-8 rounded-2xl border border-amber-500/20 hover:border-amber-400/60 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-500/20 animate-fade-in delay-400 overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-amber-400/20 to-transparent rounded-bl-full"></div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-xl">⚛️</span>
                </div>
                <h3 className="text-xl font-bold text-amber-400 mb-3 group-hover:text-amber-300 transition-colors">التقنيات الحديثة</h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  <span className="text-amber-400 font-semibold">React 18 • TypeScript</span>
                  <br />
                  Vite • Tailwind CSS • ShadCN/UI
                </p>
              </div>
            </div>

            {/* الخبرة العملية */}
            <div className="group relative bg-gradient-to-br from-gray-900/80 to-gray-800/60 p-8 rounded-2xl border border-amber-500/20 hover:border-amber-400/60 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-500/20 animate-fade-in delay-600 overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-amber-400/20 to-transparent rounded-bl-full"></div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-xl">💼</span>
                </div>
                <h3 className="text-xl font-bold text-amber-400 mb-3 group-hover:text-amber-300 transition-colors">الخبرة العملية</h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  <span className="text-amber-400 font-semibold">Laravel 11 • PHP</span>
                  <br />
                  تصميم قواعد البيانات • تطبيقات Desktop
                </p>
              </div>
            </div>

            {/* المشاريع */}
            <div className="group relative bg-gradient-to-br from-gray-900/80 to-gray-800/60 p-8 rounded-2xl border border-amber-500/20 hover:border-amber-400/60 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-500/20 animate-fade-in delay-800 overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-amber-400/20 to-transparent rounded-bl-full"></div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-xl">🚀</span>
                </div>
                <h3 className="text-xl font-bold text-amber-400 mb-3 group-hover:text-amber-300 transition-colors">المشاريع الحالية</h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  <span className="text-amber-400 font-semibold">الموقع الشخصي الاحترافي</span>
                  <br />
                  React Query • أنظمة PDF متقدمة
                </p>
              </div>
            </div>
          </div>

          {/* Technical Skills Showcase */}
          <div className="bg-gradient-to-r from-gray-900/60 to-gray-800/40 rounded-3xl p-8 border border-amber-500/30 mb-12 animate-fade-in delay-1000">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-amber-400 mb-2">🛠️ التقنيات المتقنة</h3>
              <p className="text-gray-300">مجموعة شاملة من التقنيات الحديثة والأدوات المتطورة</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {[
                { name: 'React 18', color: 'from-blue-400 to-blue-600', icon: '⚛️' },
                { name: 'TypeScript', color: 'from-blue-500 to-blue-700', icon: '📘' },
                { name: 'Vite', color: 'from-purple-400 to-purple-600', icon: '⚡' },
                { name: 'Tailwind', color: 'from-cyan-400 to-cyan-600', icon: '🎨' },
                { name: 'Laravel', color: 'from-red-400 to-red-600', icon: '🔧' },
                { name: 'GitHub', color: 'from-gray-400 to-gray-600', icon: '🐙' }
              ].map((tech, index) => (
                <div key={tech.name} className={`group bg-gradient-to-r ${tech.color} p-4 rounded-xl text-center transform hover:scale-110 transition-all duration-300 hover:shadow-lg animate-fade-in`} style={{animationDelay: `${1200 + index * 100}ms`}}>
                  <div className="text-2xl mb-2 group-hover:scale-125 transition-transform duration-300">{tech.icon}</div>
                  <div className="text-white font-semibold text-sm">{tech.name}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center animate-fade-in delay-1400">
            <div className="inline-flex flex-col sm:flex-row gap-4 items-center">
              <Link to="/about">
                <Button className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-black font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-amber-500/30 group">
                  <Download className="w-5 h-5 ml-2 group-hover:animate-bounce" />
                  تحميل السيرة الذاتية
                </Button>
              </Link>

              <Link to="/projects">
                <Button variant="outline" className="border-amber-400 text-amber-400 hover:bg-amber-400 hover:text-black px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105">
                  <span className="ml-2">🔍</span>
                  استكشف المشاريع
                </Button>
              </Link>
            </div>

            <p className="text-gray-400 mt-6 text-sm">
              📈 راقب انجازاتي التي ستضاف بشكل مستمر الى سيرتي الذاتية 🚀
            </p>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
