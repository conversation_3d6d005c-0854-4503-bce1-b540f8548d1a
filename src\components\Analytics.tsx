import { useEffect } from 'react';

// Google Analytics 4 Integration
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

interface AnalyticsProps {
  trackingId?: string;
}

const Analytics: React.FC<AnalyticsProps> = ({ trackingId = 'G-XXXXXXXXXX' }) => {
  useEffect(() => {
    // تحميل Google Analytics فقط في الإنتاج
    if (process.env.NODE_ENV === 'production' && trackingId !== 'G-XXXXXXXXXX') {
      // إنشاء script tag لـ Google Analytics
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`;
      document.head.appendChild(script);

      // إعداد Google Analytics
      window.dataLayer = window.dataLayer || [];
      window.gtag = function gtag() {
        window.dataLayer.push(arguments);
      };
      
      window.gtag('js', new Date());
      window.gtag('config', trackingId, {
        page_title: document.title,
        page_location: window.location.href,
      });

      // تتبع الأحداث المخصصة
      trackPageView();
    }
  }, [trackingId]);

  const trackPageView = () => {
    if (typeof window.gtag !== 'undefined') {
      window.gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        page_path: window.location.pathname,
      });
    }
  };

  // تتبع النقرات على الروابط الخارجية
  useEffect(() => {
    const handleExternalLinkClick = (event: MouseEvent) => {
      const target = event.target as HTMLAnchorElement;
      if (target.tagName === 'A' && target.href && target.href.startsWith('http')) {
        if (typeof window.gtag !== 'undefined') {
          window.gtag('event', 'click', {
            event_category: 'external_link',
            event_label: target.href,
            value: 1,
          });
        }
      }
    };

    document.addEventListener('click', handleExternalLinkClick);
    return () => document.removeEventListener('click', handleExternalLinkClick);
  }, []);

  return null; // هذا المكون لا يعرض أي شيء
};

// دوال مساعدة لتتبع الأحداث
export const trackEvent = (action: string, category: string, label?: string, value?: number) => {
  if (typeof window.gtag !== 'undefined') {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

export const trackTiming = (name: string, value: number, category?: string) => {
  if (typeof window.gtag !== 'undefined') {
    window.gtag('event', 'timing_complete', {
      name: name,
      value: value,
      event_category: category || 'Performance',
    });
  }
};

export default Analytics;
