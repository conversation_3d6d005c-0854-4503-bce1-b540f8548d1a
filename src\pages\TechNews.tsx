
import React from "react";
import { ExternalLink, Clock, Globe, RefreshCw, AlertCircle } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";

// بيانات أخبار تقنية ثابتة للعرض (حل مؤقت لمشكلة CORS)
const staticTechNews = [
  {
    article_id: "1",
    title: "الذكاء الاصطناعي يحدث ثورة في تطوير البرمجيات",
    description: "تقنيات الذكاء الاصطناعي الجديدة تساعد المطورين في كتابة كود أكثر كفاءة وأماناً، مما يسرع عملية التطوير بشكل كبير.",
    content: "مع التطور المستمر في تقنيات الذكاء الاصطناعي، أصبح بإمكان المطورين الاستفادة من أدوات متقدمة تساعدهم في كتابة كود أكثر جودة وكفاءة.",
    link: "https://github.com/HA1234098765",
    image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
    source_id: "TechCrunch",
    category: ["تقنية", "ذكاء اصطناعي"],
    pubDate: "2024-01-15T10:30:00Z"
  },
  {
    article_id: "2",
    title: "React 19 يجلب ميزات جديدة لتطوير الواجهات",
    description: "الإصدار الجديد من React يقدم تحسينات كبيرة في الأداء وميزات جديدة تسهل على المطورين بناء تطبيقات ويب متقدمة.",
    content: "React 19 يأتي بميزات مثل Server Components المحسنة وأدوات تطوير أفضل لتحسين تجربة المطور.",
    link: "https://react.dev",
    image_url: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
    source_id: "React Blog",
    category: ["تطوير ويب", "React"],
    pubDate: "2024-01-14T14:20:00Z"
  },
  {
    article_id: "3",
    title: "أمن المعلومات في عصر الحوسبة السحابية",
    description: "مع تزايد الاعتماد على الخدمات السحابية، تصبح حماية البيانات والمعلومات أولوية قصوى للشركات والمؤسسات.",
    content: "تقنيات الأمان الجديدة تساعد في حماية البيانات المخزنة في السحابة من التهديدات السيبرانية المتزايدة.",
    link: "https://aws.amazon.com/security/",
    image_url: "https://images.unsplash.com/photo-1563986768609-322da13575f3?w=500&h=300&fit=crop",
    source_id: "Security Weekly",
    category: ["أمن المعلومات", "حوسبة سحابية"],
    pubDate: "2024-01-13T09:15:00Z"
  },
  {
    article_id: "4",
    title: "تطوير تطبيقات الهاتف المحمول باستخدام Flutter",
    description: "Flutter يواصل نموه كإطار عمل مفضل لتطوير تطبيقات الهاتف المحمول متعددة المنصات بكفاءة عالية.",
    content: "مع الإصدارات الجديدة من Flutter، أصبح بإمكان المطورين بناء تطبيقات عالية الجودة لكل من Android و iOS بكود واحد.",
    link: "https://flutter.dev",
    image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
    source_id: "Flutter Dev",
    category: ["تطوير تطبيقات", "Flutter"],
    pubDate: "2024-01-12T16:45:00Z"
  },
  {
    article_id: "5",
    title: "قواعد البيانات الحديثة وتقنيات NoSQL",
    description: "قواعد البيانات غير العلائقية تكتسب شعبية متزايدة في التطبيقات الحديثة التي تتطلب مرونة وسرعة في التعامل مع البيانات.",
    content: "MongoDB وCassandra وغيرها من قواعد البيانات NoSQL تقدم حلولاً مبتكرة لتخزين ومعالجة البيانات الضخمة.",
    link: "https://www.mongodb.com",
    image_url: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop",
    source_id: "Database Journal",
    category: ["قواعد البيانات", "NoSQL"],
    pubDate: "2024-01-11T11:30:00Z"
  },
  {
    article_id: "6",
    title: "مستقبل تطوير الويب مع WebAssembly",
    description: "WebAssembly يفتح آفاقاً جديدة لتشغيل تطبيقات عالية الأداء في المتصفحات، مما يغير مفهوم تطوير الويب.",
    content: "مع WebAssembly، يمكن للمطورين تشغيل كود مكتوب بلغات مثل C++ وRust في المتصفح بأداء قريب من الأداء الأصلي.",
    link: "https://webassembly.org",
    image_url: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=300&fit=crop",
    source_id: "Web Dev Today",
    category: ["تطوير ويب", "WebAssembly"],
    pubDate: "2024-01-10T13:20:00Z"
  }
];

// دالة لجلب الأخبار الحقيقية عبر Serverless Proxy
const fetchTechNews = async () => {
  try {
    // محاولة استخدام Serverless Function أولاً
    const proxyEndpoints = [
      '/api/news', // Vercel/Netlify function
      'https://ha1234098765.github.io/hodifa-portfolio/api/news', // GitHub Pages fallback
      '/.netlify/functions/news' // Netlify specific
    ];

    for (const endpoint of proxyEndpoints) {
      try {
        console.log(`Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.articles && data.articles.length > 0) {
            console.log(`✅ Successfully fetched ${data.articles.length} articles from ${endpoint}`);
            console.log(`📡 Source: ${data.source}`);
            return data.articles;
          }
        }
      } catch (endpointError) {
        console.warn(`❌ Failed to fetch from ${endpoint}:`, endpointError);
        continue;
      }
    }

    // إذا فشلت جميع endpoints، حاول API مباشر في التطوير
    if (import.meta.env.DEV) {
      console.log('🔄 Trying direct API in development...');
      try {
        const API_KEY = import.meta.env.VITE_NEWSDATA_API_KEY || "1660ff496c4247c3a7d49457501feb73";
        const API_URL = `https://newsdata.io/api/1/news?apikey=${API_KEY}&language=en&category=technology&size=12`;

        const response = await fetch(API_URL);
        if (response.ok) {
          const data = await response.json();
          if (data.results && data.results.length > 0) {
            console.log('✅ Direct API call successful in development');
            return data.results;
          }
        }
      } catch (directApiError) {
        console.warn('❌ Direct API call failed:', directApiError);
      }
    }

    // كحل أخير، استخدم البيانات الثابتة
    console.log('📰 Using static fallback news');
    return staticTechNews;

  } catch (error) {
    console.error('❌ Error in fetchTechNews:', error);
    return staticTechNews;
  }
};

const TechNews = () => {
  // استخدام React Query لجلب البيانات
  const {
    data: news = [],
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['techNews'],
    queryFn: fetchTechNews,
    staleTime: 5 * 60 * 1000, // 5 دقائق
    gcTime: 10 * 60 * 1000, // 10 دقائق (تم تغيير cacheTime إلى gcTime)
    retry: 3,
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const formatDate = (dateString: string) => {
    if (!dateString) return 'تاريخ غير محدد';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'تاريخ غير صحيح';

      return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      return 'تاريخ غير صحيح';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      "technology": "bg-blue-500/20 text-blue-400",
      "تقنية": "bg-blue-500/20 text-blue-400",
      "ذكاء اصطناعي": "bg-purple-500/20 text-purple-400",
      "تطوير ويب": "bg-blue-500/20 text-blue-400",
      "قواعد البيانات": "bg-green-500/20 text-green-400",
      "تطوير واجهات": "bg-cyan-500/20 text-cyan-400",
      "أمن المعلومات": "bg-red-500/20 text-red-400",
      "اتجاهات تقنية": "bg-amber-500/20 text-amber-400",
      "science": "bg-green-500/20 text-green-400",
      "business": "bg-orange-500/20 text-orange-400",
      "entertainment": "bg-pink-500/20 text-pink-400",
      "health": "bg-emerald-500/20 text-emerald-400",
      "sports": "bg-indigo-500/20 text-indigo-400",
      "politics": "bg-slate-500/20 text-slate-400"
    };
    return colors[category?.toLowerCase()] || "bg-gray-500/20 text-gray-400";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">الأخبار</span> التقنية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-6">
              آخر الأخبار والمقالات في عالم التكنولوجيا وتطوير البرمجيات
            </p>

            {/* ملاحظة حول مصدر الأخبار */}
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 max-w-2xl mx-auto">
              <div className="flex items-center justify-center mb-2">
                <Globe className="w-5 h-5 text-green-400 ml-2" />
                <span className="text-green-400 font-semibold">🚀 أخبار حقيقية مباشرة</span>
              </div>
              <p className="text-sm text-gray-300 leading-relaxed">
      ✅اليكم آخر الأخبار والمقالات في عالم التكنولوجيا وتطوير البرمجيات من مصادر موثوقة
              </p>
            </div>
          </div>

          {/* زر إعادة التحميل */}
          <div className="flex justify-center mb-8">
            <Button
              onClick={() => refetch()}
              disabled={isLoading}
              className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-6 py-2 rounded-full transition-all duration-300 transform hover:scale-105"
            >
              <RefreshCw className={`w-4 h-4 ml-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'جاري التحميل...' : 'تحديث الأخبار'}
            </Button>
          </div>

          {isError ? (
            <div className="text-center py-12">
              <Card className="bg-red-900/20 border-red-500/20 max-w-md mx-auto">
                <CardContent className="p-8">
                  <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-red-400 mb-2">خطأ في تحميل الأخبار</h3>
                  <p className="text-gray-300 mb-4">
                    عذراً، حدث خطأ أثناء تحميل الأخبار. يرجى المحاولة مرة أخرى.
                  </p>
                  <Button
                    onClick={() => refetch()}
                    className="bg-red-500 hover:bg-red-600 text-white"
                  >
                    إعادة المحاولة
                  </Button>
                </CardContent>
              </Card>
            </div>
          ) : isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <Card key={index} className="bg-gray-900/50 border-amber-500/20 animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-gray-700 rounded mb-2"></div>
                    <div className="h-3 bg-gray-700 rounded w-3/4"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-700 rounded"></div>
                      <div className="h-3 bg-gray-700 rounded"></div>
                      <div className="h-3 bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : news.length === 0 ? (
            <div className="text-center py-12">
              <Card className="bg-gray-900/30 border-amber-500/20 max-w-md mx-auto">
                <CardContent className="p-8">
                  <Globe className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-amber-400 mb-2">لا توجد أخبار متاحة</h3>
                  <p className="text-gray-300">
                    لم يتم العثور على أخبار تقنية حالياً. يرجى المحاولة لاحقاً.
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {news.map((article: any, index: number) => (
                <Card key={article.article_id || index} className="bg-gray-900/50 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 group">
                  <CardHeader>
                    <div className="flex justify-between items-start mb-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(article.category?.[0] || 'تقنية')}`}>
                        {article.category?.[0] || 'تقنية'}
                      </span>
                      <div className="flex items-center text-xs text-gray-400">
                        <Globe className="w-3 h-3 ml-1" />
                        {article.source_id || 'مصدر غير معروف'}
                      </div>
                    </div>
                    <CardTitle className="text-lg text-amber-400 group-hover:text-amber-300 transition-colors line-clamp-2">
                      {article.title || 'عنوان غير متوفر'}
                    </CardTitle>
                  </CardHeader>

                  <CardContent>
                    {/* عرض الصورة إذا كانت متوفرة */}
                    {article.image_url && (
                      <div className="mb-4 rounded-lg overflow-hidden">
                        <img
                          src={article.image_url}
                          alt={article.title}
                          className="w-full h-32 object-cover transition-transform duration-300 group-hover:scale-105"
                          onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
                            (e.target as HTMLImageElement).style.display = 'none';
                          }}
                        />
                      </div>
                    )}

                    <p className="text-gray-300 text-sm mb-4 line-clamp-3 leading-relaxed">
                      {article.description || article.content || 'لا يوجد وصف متاح'}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-xs text-gray-400">
                        <Clock className="w-3 h-3 ml-1" />
                        {formatDate(article.pubDate)}
                      </div>

                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-amber-400 hover:text-amber-300 hover:bg-amber-500/10 transition-all duration-300"
                        asChild
                      >
                        <a href={article.link} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4 ml-1" />
                          اقرأ المزيد
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* معلومات إضافية */}
          <div className="mt-16">
            <Card className="bg-gray-900/30 border-amber-500/20 max-w-2xl mx-auto">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold text-amber-400 mb-4">🚀 تقنيتي في الأخبار المباشرة</h3>
                <p className="text-gray-300 mb-6">
                  ما أقدمه لكم :
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3">
                    <div className="text-green-400 font-semibold mb-1">🌐 مصادر حقيقية</div>
                    <div className="text-gray-300">من مواقع عالمية موثوقة</div>
                  </div>
                  <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
                    <div className="text-blue-400 font-semibold mb-1">⚡ المزامنة الفورية</div>
                    <div className="text-gray-300">في تقديم كل ماهو حديث</div>
                  </div>
                  <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-3">
                    <div className="text-purple-400 font-semibold mb-1">🔄 تحديث تلقائي</div>
                    <div className="text-gray-300">أخبار محدثة كل 5 دقائق</div>
                  </div>
                  <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-3">
                    <div className="text-orange-400 font-semibold mb-1">🎯 هدفي</div>
                    <div className="text-gray-300">ضمان ايصال الفائدة للعامة </div>
                  </div>
                </div>
                <div className="mt-4 text-xs text-gray-400">
                  * 🌟 اسعى دائما لنشر الوعي بين عامة المجتمع لذا اشكركم على اهتمامكم 🙏
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default TechNews;
