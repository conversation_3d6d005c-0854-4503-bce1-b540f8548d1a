import { useEffect } from 'react';
import { trackTiming } from './Analytics';

const PerformanceMonitor: React.FC = () => {
  useEffect(() => {
    // قياس أداء التحميل
    const measurePerformance = () => {
      if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          // قياس وقت تحميل الصفحة
          const pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
          trackTiming('page_load_time', Math.round(pageLoadTime));

          // قياس وقت الاستجابة الأولى
          const responseTime = navigation.responseEnd - navigation.requestStart;
          trackTiming('response_time', Math.round(responseTime));

          // قياس وقت DOM Content Loaded
          const domContentLoadedTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;
          trackTiming('dom_content_loaded', Math.round(domContentLoadedTime));
        }

        // قياس First Contentful Paint
        const paintEntries = performance.getEntriesByType('paint');
        paintEntries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            trackTiming('first_contentful_paint', Math.round(entry.startTime));
          }
        });

        // قياس Largest Contentful Paint
        if ('PerformanceObserver' in window) {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            if (lastEntry) {
              trackTiming('largest_contentful_paint', Math.round(lastEntry.startTime));
            }
          });
          
          try {
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
          } catch (e) {
            // المتصفح لا يدعم LCP
          }
        }
      }
    };

    // تشغيل القياس بعد تحميل الصفحة
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    // تنظيف
    return () => {
      window.removeEventListener('load', measurePerformance);
    };
  }, []);

  // مراقبة استخدام الذاكرة (إذا كان متاحاً)
  useEffect(() => {
    const monitorMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        };
        
        // إرسال بيانات الذاكرة كل 30 ثانية
        console.log('Memory Usage:', memoryUsage);
      }
    };

    const interval = setInterval(monitorMemory, 30000);
    return () => clearInterval(interval);
  }, []);

  // مراقبة الأخطاء
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('JavaScript Error:', event.error);
      // يمكن إرسال الأخطاء إلى خدمة مراقبة الأخطاء
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled Promise Rejection:', event.reason);
      // يمكن إرسال الأخطاء إلى خدمة مراقبة الأخطاء
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return null; // هذا المكون لا يعرض أي شيء
};

export default PerformanceMonitor;
