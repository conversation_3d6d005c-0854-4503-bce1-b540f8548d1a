// أنواع البيانات لنظام PDF

export interface PersonalInfo {
  name: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
}

export interface Education {
  institution: string;
  degree: string;
  status: string;
  year: string;
}

export interface Experience {
  title: string;
  company: string;
  description: string;
  year: string;
}

export interface Project {
  name: string;
  description: string;
  tech: string;
}

export interface Skills {
  frontend: string[];
  backend: string[];
  database: string[];
  tools: string[];
  soft: string[];
}

export interface Language {
  name: string;
  level: string;
}

export interface CVData {
  personalInfo: PersonalInfo;
  summary: string;
  education: Education[];
  experience: Experience[];
  projects: Project[];
  skills: Skills;
  languages: Language[];
  courses: string[];
}

export interface FontOption {
  name: string;
  description: string;
  family: string;
}

export interface ArabicFontOptions {
  [key: string]: FontOption;
}

export interface PDFGeneratorProps {
  className?: string;
  data?: CVData;
  fontFamily?: string;
}

export interface DownloadButtonProps {
  onDownload: () => Promise<void> | void;
  fileName?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children?: React.ReactNode;
}

export interface PDFPreviewProps {
  className?: string;
  data?: CVData;
}

export type DownloadStatus = 'idle' | 'downloading' | 'success' | 'error';

export interface FontLoadingState {
  isLoading: boolean;
  isLoaded: boolean;
  error?: string;
}

// ثوابت النظام
export const PDF_CONFIG = {
  DEFAULT_FONT: 'Amiri',
  FALLBACK_FONT: 'Arial',
  PAGE_SIZE: 'A4' as const,
  ORIENTATION: 'portrait' as const,
  MARGINS: {
    top: 30,
    bottom: 30,
    left: 30,
    right: 30,
  },
  COLORS: {
    primary: '#f59e0b',
    secondary: '#6b7280',
    text: '#374151',
    background: '#ffffff',
  },
} as const;

export const FONT_URLS = {
  amiri: {
    normal: 'https://fonts.gstatic.com/s/amiri/v27/J7aRnpd8CGxBHqUpvrIw74NL.woff2',
    bold: 'https://fonts.gstatic.com/s/amiri/v27/J7afnpd8CGxBHqUpvrIw-xNLxQs.woff2',
  },
  cairo: {
    normal: 'https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8gfxJkAg.woff2',
    bold: 'https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8gfxJkAg.woff2',
  },
  tajawal: {
    normal: 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2',
    bold: 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2',
  },
  lateef: {
    normal: 'https://fonts.gstatic.com/s/lateef/v24/hESw6XVnNCxEvkbMpheEZo_H_w.woff2',
  },
  notoSansArabic: {
    normal: 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu3CBFQLaig.woff2',
    bold: 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu3CBFQLaig.woff2',
  },
} as const;

export default {
  PersonalInfo,
  Education,
  Experience,
  Project,
  Skills,
  Language,
  CVData,
  FontOption,
  ArabicFontOptions,
  PDFGeneratorProps,
  DownloadButtonProps,
  PDFPreviewProps,
  DownloadStatus,
  FontLoadingState,
  PDF_CONFIG,
  FONT_URLS,
};
