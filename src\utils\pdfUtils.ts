// أدوات مساعدة لنظام PDF

import { CVData } from '@/types/pdf';

/**
 * تنسيق التاريخ للعرض في PDF
 */
export const formatDateForPDF = (date: Date = new Date()): string => {
  return date.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

/**
 * إنشاء اسم ملف PDF مع التاريخ
 */
export const generatePDFFileName = (baseName: string = 'السيرة_الذاتية'): string => {
  const date = new Date().toLocaleDateString('ar-EG').replace(/\//g, '-');
  return `${baseName}_${date}.pdf`;
};

/**
 * تنظيف النص للعرض في PDF
 */
export const sanitizeTextForPDF = (text: string): string => {
  return text
    .replace(/[\u200B-\u200D\uFEFF]/g, '') // إزالة الأحرف غير المرئية
    .replace(/\s+/g, ' ') // تنظيف المسافات الزائدة
    .trim();
};

/**
 * تقسيم النص الطويل إلى أسطر للعرض في PDF
 */
export const splitTextForPDF = (text: string, maxLength: number = 80): string[] => {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  for (const word of words) {
    if ((currentLine + word).length <= maxLength) {
      currentLine += (currentLine ? ' ' : '') + word;
    } else {
      if (currentLine) {
        lines.push(currentLine);
      }
      currentLine = word;
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines;
};

/**
 * التحقق من صحة بيانات السيرة الذاتية
 */
export const validateCVData = (data: CVData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // التحقق من المعلومات الشخصية
  if (!data.personalInfo.name.trim()) {
    errors.push('الاسم مطلوب');
  }
  if (!data.personalInfo.email.trim()) {
    errors.push('البريد الإلكتروني مطلوب');
  }
  if (!data.personalInfo.phone.trim()) {
    errors.push('رقم الهاتف مطلوب');
  }

  // التحقق من الملخص المهني
  if (!data.summary.trim()) {
    errors.push('الملخص المهني مطلوب');
  }

  // التحقق من التعليم
  if (data.education.length === 0) {
    errors.push('يجب إضافة معلومة تعليمية واحدة على الأقل');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * تحويل البيانات إلى تنسيق مناسب للطباعة
 */
export const formatDataForPrint = (data: CVData): CVData => {
  return {
    ...data,
    personalInfo: {
      ...data.personalInfo,
      name: sanitizeTextForPDF(data.personalInfo.name),
      title: sanitizeTextForPDF(data.personalInfo.title),
      email: sanitizeTextForPDF(data.personalInfo.email),
      phone: sanitizeTextForPDF(data.personalInfo.phone),
      location: sanitizeTextForPDF(data.personalInfo.location),
      website: data.personalInfo.website ? sanitizeTextForPDF(data.personalInfo.website) : undefined,
    },
    summary: sanitizeTextForPDF(data.summary),
    education: data.education.map(edu => ({
      ...edu,
      institution: sanitizeTextForPDF(edu.institution),
      degree: sanitizeTextForPDF(edu.degree),
      status: sanitizeTextForPDF(edu.status),
      year: sanitizeTextForPDF(edu.year),
    })),
    experience: data.experience.map(exp => ({
      ...exp,
      title: sanitizeTextForPDF(exp.title),
      company: sanitizeTextForPDF(exp.company),
      description: sanitizeTextForPDF(exp.description),
      year: sanitizeTextForPDF(exp.year),
    })),
    projects: data.projects.map(project => ({
      ...project,
      name: sanitizeTextForPDF(project.name),
      description: sanitizeTextForPDF(project.description),
      tech: sanitizeTextForPDF(project.tech),
    })),
    skills: {
      frontend: data.skills.frontend.map(skill => sanitizeTextForPDF(skill)),
      backend: data.skills.backend.map(skill => sanitizeTextForPDF(skill)),
      database: data.skills.database.map(skill => sanitizeTextForPDF(skill)),
      tools: data.skills.tools.map(skill => sanitizeTextForPDF(skill)),
      soft: data.skills.soft.map(skill => sanitizeTextForPDF(skill)),
    },
    languages: data.languages.map(lang => ({
      name: sanitizeTextForPDF(lang.name),
      level: sanitizeTextForPDF(lang.level),
    })),
    courses: data.courses.map(course => sanitizeTextForPDF(course)),
  };
};

/**
 * حساب عدد الصفحات المتوقع للسيرة الذاتية
 */
export const estimatePageCount = (data: CVData): number => {
  let contentHeight = 0;
  const pageHeight = 800; // تقدير ارتفاع الصفحة
  const lineHeight = 20; // تقدير ارتفاع السطر

  // المعلومات الشخصية والملخص
  contentHeight += 150; // header
  contentHeight += Math.ceil(data.summary.length / 80) * lineHeight; // summary

  // التعليم
  contentHeight += 50 + (data.education.length * 60);

  // الخبرة
  contentHeight += 50 + (data.experience.length * 80);

  // المشاريع
  contentHeight += 50 + (data.projects.length * 80);

  // المهارات
  const totalSkills = data.skills.frontend.length + data.skills.backend.length + 
                     data.skills.database.length + data.skills.tools.length + data.skills.soft.length;
  contentHeight += 50 + Math.ceil(totalSkills / 5) * 30;

  // اللغات والدورات
  contentHeight += 50 + (data.languages.length * 25);
  contentHeight += 50 + (data.courses.length * 25);

  return Math.ceil(contentHeight / pageHeight);
};

/**
 * إنشاء معاينة نصية للسيرة الذاتية
 */
export const generateTextPreview = (data: CVData): string => {
  const lines: string[] = [];

  // المعلومات الشخصية
  lines.push(data.personalInfo.name);
  lines.push(data.personalInfo.title);
  lines.push('');

  // معلومات التواصل
  lines.push('معلومات التواصل:');
  lines.push(`البريد الإلكتروني: ${data.personalInfo.email}`);
  lines.push(`الهاتف: ${data.personalInfo.phone}`);
  lines.push(`الموقع: ${data.personalInfo.location}`);
  if (data.personalInfo.website) {
    lines.push(`الموقع الإلكتروني: ${data.personalInfo.website}`);
  }
  lines.push('');

  // الملخص المهني
  lines.push('الملخص المهني:');
  lines.push(data.summary);
  lines.push('');

  // التعليم
  if (data.education.length > 0) {
    lines.push('التعليم:');
    data.education.forEach(edu => {
      lines.push(`• ${edu.institution} - ${edu.degree} (${edu.year})`);
    });
    lines.push('');
  }

  // الخبرة
  if (data.experience.length > 0) {
    lines.push('الخبرة العملية:');
    data.experience.forEach(exp => {
      lines.push(`• ${exp.title} في ${exp.company} (${exp.year})`);
      lines.push(`  ${exp.description}`);
    });
    lines.push('');
  }

  // المشاريع
  if (data.projects.length > 0) {
    lines.push('المشاريع:');
    data.projects.forEach(project => {
      lines.push(`• ${project.name}`);
      lines.push(`  ${project.description}`);
      lines.push(`  التقنيات: ${project.tech}`);
    });
    lines.push('');
  }

  return lines.join('\n');
};

/**
 * تحويل البيانات إلى JSON للتصدير
 */
export const exportCVDataAsJSON = (data: CVData): string => {
  return JSON.stringify(data, null, 2);
};

/**
 * استيراد البيانات من JSON
 */
export const importCVDataFromJSON = (jsonString: string): CVData | null => {
  try {
    const data = JSON.parse(jsonString);
    const validation = validateCVData(data);
    
    if (validation.isValid) {
      return data;
    } else {
      console.error('بيانات غير صحيحة:', validation.errors);
      return null;
    }
  } catch (error) {
    console.error('خطأ في تحليل JSON:', error);
    return null;
  }
};

/**
 * إنشاء نسخة احتياطية من البيانات
 */
export const createDataBackup = (data: CVData): void => {
  const backup = {
    data,
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  };

  const blob = new Blob([JSON.stringify(backup, null, 2)], { 
    type: 'application/json' 
  });
  
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `cv_backup_${formatDateForPDF().replace(/\s/g, '_')}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export default {
  formatDateForPDF,
  generatePDFFileName,
  sanitizeTextForPDF,
  splitTextForPDF,
  validateCVData,
  formatDataForPrint,
  estimatePageCount,
  generateTextPreview,
  exportCVDataAsJSON,
  importCVDataFromJSON,
  createDataBackup,
};
