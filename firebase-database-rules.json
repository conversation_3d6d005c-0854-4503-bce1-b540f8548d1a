{"rules": {"messages": {".read": true, ".write": "auth == null", ".indexOn": ["timestamp"], "$messageId": {".validate": "newData.hasChildren(['message', 'username', 'timestamp', 'userId']) && newData.child('message').isString() && newData.child('message').val().length >= 1 && newData.child('message').val().length <= 500 && newData.child('username').isString() && newData.child('username').val().length >= 2 && newData.child('username').val().length <= 20 && newData.child('timestamp').isNumber() && newData.child('userId').isString() && newData.child('userId').val().length >= 10", ".write": "!data.exists() && newData.exists()"}}, "onlineUsers": {".read": true, ".write": true, "$userId": {".validate": "newData.isNumber() && newData.val() >= 0"}}, "rateLimiting": {"$userId": {".read": false, ".write": "!data.exists() || data.val() < (now - 1000)", ".validate": "newData.val() == now"}}}}