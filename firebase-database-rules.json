{"rules": {"messages": {".read": true, ".write": true, ".indexOn": ["timestamp"], "$messageId": {".validate": "newData.hasChildren(['message', 'username', 'timestamp', 'userId']) && newData.child('message').isString() && newData.child('message').val().length <= 500 && newData.child('username').isString() && newData.child('username').val().length <= 20 && newData.child('timestamp').isNumber() && newData.child('userId').isString()"}}, "onlineUsers": {".read": true, ".write": true}}}