import React, { useState, useEffect, useRef } from 'react';
import { ref, push, onValue, off, serverTimestamp } from 'firebase/database';
import { database } from '@/config/firebaseConfig';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Send, Users, MessageCircle, Clock } from 'lucide-react';

// تعريف نوع الرسالة
interface Message {
  id: string;
  username: string;
  message: string;
  timestamp: number;
  userId: string;
}

// تعريف نوع المستخدم
interface User {
  username: string;
  userId: string;
}

const LiveGroupChat: React.FC = () => {
  // حالات المكون
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [user, setUser] = useState<User | null>(null);
  const [tempUsername, setTempUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<number>(0);
  
  // مراجع للعناصر
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // دالة للتمرير إلى أسفل الدردشة
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // تأثير للتمرير عند إضافة رسائل جديدة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // تأثير لتحميل المستخدم من localStorage
  useEffect(() => {
    const savedUser = localStorage.getItem('chatUser');
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('chatUser');
      }
    }
  }, []);

  // تأثير لتحميل الرسائل من Firebase
  useEffect(() => {
    if (!user) return;

    const messagesRef = ref(database, 'messages');
    
    // الاستماع للرسائل الجديدة
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messagesList: Message[] = Object.keys(data).map(key => ({
          id: key,
          ...data[key]
        }));
        
        // ترتيب الرسائل حسب التوقيت
        messagesList.sort((a, b) => a.timestamp - b.timestamp);
        setMessages(messagesList);
      } else {
        setMessages([]);
      }
    });

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      off(messagesRef, 'value', unsubscribe);
    };
  }, [user]);

  // دالة لحفظ اسم المستخدم
  const handleSaveUsername = () => {
    if (tempUsername.trim().length < 2) {
      alert('يجب أن يكون الاسم المستعار أكثر من حرفين');
      return;
    }

    const userId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newUser: User = {
      username: tempUsername.trim(),
      userId: userId
    };

    setUser(newUser);
    localStorage.setItem('chatUser', JSON.stringify(newUser));
    setTempUsername('');
  };

  // دالة لإرسال رسالة جديدة
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user || isLoading) return;

    setIsLoading(true);
    
    try {
      const messagesRef = ref(database, 'messages');
      const messageData = {
        username: user.username,
        message: newMessage.trim(),
        timestamp: Date.now(),
        userId: user.userId
      };

      await push(messagesRef, messageData);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة للتعامل مع الضغط على Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!user) {
        handleSaveUsername();
      } else {
        handleSendMessage();
      }
    }
  };

  // دالة لتنسيق التوقيت
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // دالة لتغيير اسم المستخدم
  const handleChangeUsername = () => {
    setUser(null);
    localStorage.removeItem('chatUser');
  };

  // إذا لم يتم تعيين اسم المستخدم، عرض نموذج الاسم
  if (!user) {
    return (
      <Card className="w-full max-w-md mx-auto bg-gray-900/50 border-amber-500/20">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl text-amber-400 flex items-center justify-center gap-2">
            <MessageCircle className="w-6 h-6" />
            الدردشة الجماعية
          </CardTitle>
          <p className="text-gray-300 text-sm">
            أدخل اسمك المستعار للانضمام إلى الدردشة
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            type="text"
            placeholder="اسمك المستعار..."
            value={tempUsername}
            onChange={(e) => setTempUsername(e.target.value)}
            onKeyPress={handleKeyPress}
            className="bg-gray-800/50 border-amber-500/30 text-white placeholder-gray-400"
            maxLength={20}
          />
          <Button
            onClick={handleSaveUsername}
            disabled={tempUsername.trim().length < 2}
            className="w-full bg-amber-500 hover:bg-amber-600 text-black font-semibold"
          >
            انضم إلى الدردشة
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto bg-gray-900/50 border-amber-500/20 h-[600px] flex flex-col">
      {/* رأس الدردشة */}
      <CardHeader className="border-b border-amber-500/20 pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl text-amber-400 flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            الدردشة الجماعية المباشرة
          </CardTitle>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="border-green-500/50 text-green-400">
              <Users className="w-3 h-3 mr-1" />
              متصل: {messages.length > 0 ? new Set(messages.map(m => m.userId)).size : 0}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleChangeUsername}
              className="border-amber-500/50 text-amber-400 hover:bg-amber-500/10"
            >
              تغيير الاسم
            </Button>
          </div>
        </div>
        <p className="text-sm text-gray-400">
          مرحباً <span className="text-amber-400 font-semibold">{user.username}</span>! 
          شارك في الدردشة مع زوار الموقع الآخرين
        </p>
      </CardHeader>

      {/* منطقة الرسائل */}
      <CardContent className="flex-1 p-0 overflow-hidden">
        <ScrollArea className="h-full p-4" ref={chatContainerRef}>
          <div className="space-y-3">
            {messages.length === 0 ? (
              <div className="text-center py-8">
                <MessageCircle className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-400">لا توجد رسائل بعد. كن أول من يبدأ المحادثة!</p>
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.userId === user.userId ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.userId === user.userId
                        ? 'bg-amber-500 text-black'
                        : 'bg-gray-700 text-white'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className={`text-xs font-semibold ${
                        message.userId === user.userId ? 'text-black/70' : 'text-amber-400'
                      }`}>
                        {message.userId === user.userId ? 'أنت' : message.username}
                      </span>
                      <span className={`text-xs flex items-center gap-1 ${
                        message.userId === user.userId ? 'text-black/60' : 'text-gray-400'
                      }`}>
                        <Clock className="w-3 h-3" />
                        {formatTime(message.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm break-words">{message.message}</p>
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>

      {/* منطقة إدخال الرسالة */}
      <div className="border-t border-amber-500/20 p-4">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="اكتب رسالتك هنا..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
            className="flex-1 bg-gray-800/50 border-amber-500/30 text-white placeholder-gray-400"
            maxLength={500}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isLoading}
            className="bg-amber-500 hover:bg-amber-600 text-black px-6"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          اضغط Enter للإرسال • الحد الأقصى 500 حرف
        </p>
      </div>
    </Card>
  );
};

export default LiveGroupChat;
