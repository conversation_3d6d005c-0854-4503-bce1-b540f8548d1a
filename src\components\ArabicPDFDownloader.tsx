import React, { useState } from 'react';
import { Download, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

const ArabicPDFDownloader: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  const generateArabicPDF = async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);
      
      toast.loading('جاري إنشاء ملف PDF...', {
        id: 'arabic-pdf',
        description: 'يتم تحضير السيرة الذاتية باللغة العربية',
      });

      // انتظار قصير لإظهار التحميل
      await new Promise(resolve => setTimeout(resolve, 1500));

      // تحويل صورة البروفايل إلى base64
      const getProfileImageBase64 = async (): Promise<string> => {
        try {
          const response = await fetch('/profile-photo.jpg');
          const blob = await response.blob();
          return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.readAsDataURL(blob);
          });
        } catch (error) {
          console.error('خطأ في تحميل صورة البروفايل:', error);
          return '';
        }
      };

      const profileImageBase64 = await getProfileImageBase64();

      // إنشاء محتوى PDF بصيغة نصية مع دعم العربية
      const arabicContent = `
السيرة الذاتية
${profileImageBase64 ? '[تتضمن صورة البروفايل الشخصية]' : ''}

حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
مهندس تقنية معلومات

معلومات التواصل:
البريد الإلكتروني: <EMAIL>
الهاتف: +967 777548421 / +967 718706242
الموقع: عدن، اليمن
الموقع الإلكتروني: www.hodifatech.com

الملخص المهني:
طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة شاملة في تطوير التطبيقات الحديثة باستخدام أحدث التقنيات. أتقن تطوير الواجهات الأمامية باستخدام React 18 مع TypeScript، وأستخدم أدوات البناء المتقدمة مثل Vite لتطوير سريع وفعال. لدي خبرة عملية في بناء مواقع ويب متكاملة باستخدام Tailwind CSS و ShadCN/UI لتصميم واجهات مستخدم احترافية ومتجاوبة.

أعمل حالياً على تطوير موقعي الشخصي الاحترافي (www.hodifatech.com) باستخدام React 18, TypeScript, Vite, Tailwind CSS, React Router, React Query, وأنظمة PDF متقدمة. كما لدي خبرة في تطوير الخلفية باستخدام Laravel 11 و PHP، وخبرة في قواعد البيانات SQL Server و MySQL. أتقن استخدام Git و GitHub لإدارة المشاريع، وأستخدم ESLint و Vitest للجودة والاختبار.

لدي معرفة في هندسة وتحليل البرمجيات وأنماط التصميم (Design Patterns)، وأسعى دائماً لتعلم التقنيات الجديدة وتطوير مهاراتي العملية في مجال تطوير الويب الحديث.

التعليم:
• جامعة عدن - كلية الهندسة
  بكالوريوس تقنية المعلومات - السنة الرابعة
  2021 - 2025

الخبرة العملية:
• شريك في تحليل قواعد البيانات
  معهد تقني - 2023
  عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية

• تاجر حر
  العمل الحر - 2022 - الحاضر
  أعمل في مجال التجارة الحرة

المشاريع:
• الموقع الشخصي الاحترافي - www.hodifatech.com
  التقنيات: React 18, TypeScript, Vite, Tailwind CSS, ShadCN/UI, React Router, React Query
  تطوير موقع شخصي احترافي متكامل يضم البورتفوليو، الأخبار التقنية، نظام PDF متقدم، وواجهات تفاعلية حديثة مع دعم كامل للغة العربية ونشر على GitHub Pages

• متجر إلكتروني - Laravel 11
  التقنيات: Laravel 11, PHP, MySQL, Blade Templates
  بناء موقع تجاري متكامل بإستخدام Laravel 11 متخصص في احتياجات الأطفال حديثي الولادة مع نظام إدارة المنتجات والطلبات

• برنامج إدارة الحجز
  التقنيات: C#, Windows Forms, SQL Server
  تطوير تطبيق desktop شامل لإدارة عمليات الحجز مع واجهة مستخدم بديهية وقاعدة بيانات متكاملة

• تصميم قواعد البيانات للمدارس
  التقنيات: SQL Server, Database Design, ERD
  تحليل وتصميم قواعد بيانات شاملة لإدارة المدارس مع تحسين الأداء وضمان سلامة البيانات

المهارات التقنية:

تطوير الواجهات الأمامية:
React 18, TypeScript, HTML5, CSS3, JavaScript ES6+, Tailwind CSS, ShadCN/UI, React Router DOM, Lucide React

إدارة الحالة والبيانات:
React Query (TanStack Query), React Hooks, Context API

أدوات البناء والتطوير:
Vite, ESLint, TypeScript, PostCSS, Autoprefixer

تطوير الخلفية:
PHP, Laravel 11, C++, C#, Node.js

قواعد البيانات:
SQL Server, MySQL, SQL

مكتبات ومكونات UI:
Radix UI, ShadCN/UI, Tailwind CSS, CSS Modules, Responsive Design

أدوات PDF والمستندات:
@react-pdf/renderer, jsPDF, pdf-lib

الاختبار وضمان الجودة:
Vitest, Testing Library, Jest DOM, ESLint

إدارة المشاريع والنشر:
Git, GitHub, GitHub Pages, Cross-env, NPM Scripts

أدوات التصميم:
Figma, Canva, Adobe Tools

المهارات الشخصية:
سريع التعلم, مهارة التواصل, العمل ضمن فريق, مواكبة التقنيات الجديدة

اللغات:
• العربية: الأم
• الإنجليزية: ممتاز
• الفرنسية: متوسط

الدورات والشهادات:
• أساسيات البرمجة و C++ - منصة Programming Advices (2022-2024)
• تطوير تطبيقات سطح المكتب - منصة Programming Advices (2024)
• تطوير المواقع Frontend - منصة Alzero Web School
• تطوير المواقع Backend - أكاديمية الجيل العربي
• React 18 و TypeScript - التعلم الذاتي والممارسة العملية (2024-2025)
• Tailwind CSS و ShadCN/UI - التطوير العملي (2024-2025)
• Vite و أدوات البناء الحديثة - الممارسة العملية (2024-2025)
• React Query و إدارة البيانات - التطبيق العملي (2024-2025)
• أنظمة PDF المتقدمة - التطوير والتطبيق (2024-2025)
• اللغة الإنجليزية - معهد أميدست الأمريكي (سنة كاملة)
• CCNA - سبتمبر 2023
• CPS - معهد أميديست الأمريكي

تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-EG')}
© 2025 حذيفه الحذيفي - جميع الحقوق محفوظة
www.hodifatech.com
      `;

      // إنشاء ملف PDF بسيط باستخدام HTML
      const htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السيرة الذاتية - حذيفه الحذيفي</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap');
        
        body {
            font-family: 'Amiri', 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.8;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #f59e0b;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .profile-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #f59e0b;
            margin: 0 auto 20px auto;
            display: block;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .name {
            font-size: 28px;
            font-weight: bold;
            color: #f59e0b;
            margin-bottom: 10px;
            font-family: 'Cairo', sans-serif;
        }
        
        .title {
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .contact {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #f59e0b;
            border-bottom: 2px solid #f59e0b;
            padding-bottom: 8px;
            margin-bottom: 15px;
            font-family: 'Cairo', sans-serif;
        }
        
        .content {
            font-size: 14px;
            line-height: 1.8;
        }
        
        .item {
            margin-bottom: 15px;
            padding-right: 15px;
            border-right: 3px solid #f59e0b;
        }
        
        .item-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .item-subtitle {
            color: #666;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .skill-category {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #f59e0b;
        }
        
        .skill-category h4 {
            color: #f59e0b;
            margin-bottom: 10px;
            font-family: 'Cairo', sans-serif;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #f59e0b;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { margin: 0; padding: 15px; }
            .header { page-break-after: avoid; }
            .section { page-break-inside: avoid; }
            .profile-image {
                width: 120px;
                height: 120px;
                margin-bottom: 15px;
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        ${profileImageBase64 ? `<img src="${profileImageBase64}" alt="صورة البروفايل" class="profile-image">` : ''}
        <div class="name">حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي</div>
        <div class="title">مهندس تقنية معلومات</div>
        <div class="contact">
            <span>📧 <EMAIL></span>
            <span>📱 +967 777548421</span>
            <span>📍 عدن، اليمن</span>
            <span>🌐 www.hodifatech.com</span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">الملخص المهني</div>
        <div class="content">
            طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة شاملة في تطوير التطبيقات الحديثة باستخدام أحدث التقنيات. أتقن تطوير الواجهات الأمامية باستخدام React 18 مع TypeScript، وأستخدم أدوات البناء المتقدمة مثل Vite لتطوير سريع وفعال. لدي خبرة عملية في بناء مواقع ويب متكاملة باستخدام Tailwind CSS و ShadCN/UI لتصميم واجهات مستخدم احترافية ومتجاوبة.
            <br><br>
            أعمل حالياً على تطوير موقعي الشخصي الاحترافي (www.hodifatech.com) باستخدام React 18, TypeScript, Vite, Tailwind CSS, React Router, React Query, وأنظمة PDF متقدمة. كما لدي خبرة في تطوير الخلفية باستخدام Laravel 11 و PHP، وخبرة في قواعد البيانات SQL Server و MySQL. أتقن استخدام Git و GitHub لإدارة المشاريع، وأستخدم ESLint و Vitest للجودة والاختبار.
            <br><br>
            لدي معرفة في هندسة وتحليل البرمجيات وأنماط التصميم (Design Patterns)، وأسعى دائماً لتعلم التقنيات الجديدة وتطوير مهاراتي العملية في مجال تطوير الويب الحديث.
        </div>
    </div>

    <div class="section">
        <div class="section-title">التعليم</div>
        <div class="item">
            <div class="item-title">جامعة عدن - كلية الهندسة</div>
            <div class="item-subtitle">2021 - 2025</div>
            <div class="content">بكالوريوس تقنية المعلومات - السنة الرابعة</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">المهارات التقنية</div>
        <div class="skills-grid">
            <div class="skill-category">
                <h4>تطوير الواجهات الأمامية</h4>
                <div>React 18, TypeScript, HTML5, CSS3, JavaScript ES6+, Tailwind CSS, ShadCN/UI, React Router DOM, Lucide React</div>
            </div>
            <div class="skill-category">
                <h4>إدارة الحالة والبيانات</h4>
                <div>React Query (TanStack Query), React Hooks, Context API</div>
            </div>
            <div class="skill-category">
                <h4>أدوات البناء والتطوير</h4>
                <div>Vite, ESLint, TypeScript, PostCSS, Autoprefixer</div>
            </div>
            <div class="skill-category">
                <h4>تطوير الخلفية</h4>
                <div>PHP, Laravel 11, C++, C#, Node.js</div>
            </div>
            <div class="skill-category">
                <h4>قواعد البيانات</h4>
                <div>SQL Server, MySQL, SQL</div>
            </div>
            <div class="skill-category">
                <h4>مكتبات ومكونات UI</h4>
                <div>Radix UI, ShadCN/UI, Tailwind CSS, CSS Modules, Responsive Design</div>
            </div>
            <div class="skill-category">
                <h4>أدوات PDF والمستندات</h4>
                <div>@react-pdf/renderer, jsPDF, pdf-lib</div>
            </div>
            <div class="skill-category">
                <h4>الاختبار وضمان الجودة</h4>
                <div>Vitest, Testing Library, Jest DOM, ESLint</div>
            </div>
            <div class="skill-category">
                <h4>إدارة المشاريع والنشر</h4>
                <div>Git, GitHub, GitHub Pages, Cross-env, NPM Scripts</div>
            </div>
            <div class="skill-category">
                <h4>أدوات التصميم</h4>
                <div>Figma, Canva, Adobe Tools</div>
            </div>
        </div>
    </div>

    <div class="footer">
        <div>تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-EG')}</div>
        <div>© 2025 حذيفه الحذيفي - جميع الحقوق محفوظة</div>
        <div>www.hodifatech.com</div>
    </div>
</body>
</html>
      `;

      // إنشاء Blob وتحميل الملف
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const fileName = `حذيفه_الحذيفي_السيرة_الذاتية_${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}.html`;

      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }, 100);

      // أيضاً إنشاء نسخة نصية
      const textBlob = new Blob([arabicContent], { type: 'text/plain;charset=utf-8' });
      const textUrl = URL.createObjectURL(textBlob);
      const textFileName = `حذيفه_الحذيفي_السيرة_الذاتية_${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}.txt`;

      const textLink = document.createElement('a');
      textLink.href = textUrl;
      textLink.download = textFileName;
      textLink.style.display = 'none';

      document.body.appendChild(textLink);
      textLink.click();

      setTimeout(() => {
        document.body.removeChild(textLink);
        URL.revokeObjectURL(textUrl);
      }, 200);

      toast.success('تم تحميل السيرة الذاتية بنجاح!', {
        id: 'arabic-pdf',
        description: `تم حفظ الملفين مع صورة البروفايل: ${fileName} و ${textFileName}`,
        duration: 5000,
      });

    } catch (error) {
      console.error('خطأ في إنشاء الملفات:', error);
      
      toast.error('فشل في تحميل السيرة الذاتية', {
        id: 'arabic-pdf',
        description: 'حدث خطأ أثناء إنشاء الملفات. يرجى المحاولة مرة أخرى.',
        duration: 5000,
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <Button
        onClick={generateArabicPDF}
        disabled={isGenerating}
        className={`
          w-full bg-gradient-to-r from-amber-500 to-amber-600 
          hover:from-amber-600 hover:to-amber-700 
          text-black font-bold text-lg px-8 py-4 
          rounded-xl shadow-lg hover:shadow-xl 
          transition-all duration-300 transform hover:scale-105
          disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          ${isGenerating ? 'animate-pulse cursor-wait' : ''}
        `}
      >
        {isGenerating ? (
          <>
            <Loader2 className="w-5 h-5 ml-2 animate-spin" />
            جاري إنشاء الملفات...
          </>
        ) : (
          <>
            <Download className="w-5 h-5 ml-2" />
            تحميل السيرة الذاتية
          </>
        )}
      </Button>

      <div className="text-center space-y-2">
        <div className="text-sm text-gray-400">
          📈 راقب انجازاتي التي ستضاف بشكل مستمر الى سيرتي الذاتية 🚀        </div>

        {isGenerating && (
          <div className="w-full max-w-xs">
            <div className="bg-gray-200 rounded-full h-1.5 overflow-hidden">
              <div className="bg-amber-500 h-1.5 rounded-full animate-pulse w-full"></div>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              جاري إنشاء الملفات بدعم كامل للعربية...
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArabicPDFDownloader;
