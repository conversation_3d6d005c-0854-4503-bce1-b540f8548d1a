import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Palette, 
  Check, 
  Eye, 
  AlertTriangle,
  Info
} from 'lucide-react';
import { 
  allThemes, 
  defaultTheme, 
  getThemePreviewColors,
  validateThemeAccessibility,
  type PDFTheme 
} from '@/styles/pdfThemes';
import { cn } from '@/lib/utils';

interface ThemeSelectorProps {
  selectedTheme?: PDFTheme;
  onThemeChange: (theme: PDFTheme) => void;
  className?: string;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  selectedTheme = defaultTheme,
  onThemeChange,
  className = ''
}) => {
  const [previewTheme, setPreviewTheme] = useState<PDFTheme | null>(null);

  const handleThemeSelect = (theme: PDFTheme) => {
    onThemeChange(theme);
    setPreviewTheme(null);
  };

  const handlePreview = (theme: PDFTheme) => {
    setPreviewTheme(theme);
  };

  const clearPreview = () => {
    setPreviewTheme(null);
  };

  const currentTheme = previewTheme || selectedTheme;

  return (
    <div className={cn('space-y-6', className)}>
      {/* معاينة القالب الحالي */}
      <Card className="bg-gray-900/50 border-amber-500/20">
        <CardHeader>
          <CardTitle className="text-amber-400 flex items-center">
            <Palette className="w-5 h-5 ml-2" />
            القالب الحالي: {currentTheme.displayName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* معاينة الألوان */}
            <div>
              <h4 className="text-sm font-semibold mb-2">الألوان:</h4>
              <div className="flex gap-2 flex-wrap">
                {Object.entries(getThemePreviewColors(currentTheme)).map(([name, color]) => (
                  <div key={name} className="flex items-center gap-2">
                    <div 
                      className="w-6 h-6 rounded border border-gray-600"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-xs text-gray-400 capitalize">
                      {name === 'primary' ? 'أساسي' :
                       name === 'secondary' ? 'ثانوي' :
                       name === 'accent' ? 'مميز' : 'خلفية'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* معاينة الخطوط */}
            <div>
              <h4 className="text-sm font-semibold mb-2">الخطوط:</h4>
              <div className="space-y-1 text-sm text-gray-300">
                <div>الأساسي: {currentTheme.fonts.primary}</div>
                <div>الثانوي: {currentTheme.fonts.secondary}</div>
                <div>العناوين: {currentTheme.fonts.heading}</div>
              </div>
            </div>

            {/* التحقق من إمكانية الوصول */}
            <div>
              <h4 className="text-sm font-semibold mb-2">إمكانية الوصول:</h4>
              {(() => {
                const accessibility = validateThemeAccessibility(currentTheme);
                return (
                  <div className="flex items-center gap-2">
                    {accessibility.isAccessible ? (
                      <>
                        <Check className="w-4 h-4 text-green-500" />
                        <Badge variant="default" className="bg-green-600">
                          ممتاز
                        </Badge>
                      </>
                    ) : (
                      <>
                        <AlertTriangle className="w-4 h-4 text-yellow-500" />
                        <Badge variant="secondary" className="bg-yellow-600">
                          يحتاج تحسين
                        </Badge>
                      </>
                    )}
                    {accessibility.issues.length > 0 && (
                      <div className="text-xs text-yellow-400">
                        {accessibility.issues.join(', ')}
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>

            {/* معاينة النص */}
            <div>
              <h4 className="text-sm font-semibold mb-2">معاينة النص:</h4>
              <div 
                className="p-4 rounded border"
                style={{ 
                  backgroundColor: currentTheme.colors.background,
                  borderColor: currentTheme.colors.border,
                  color: currentTheme.colors.text,
                  fontFamily: currentTheme.fonts.primary
                }}
              >
                <h3 
                  style={{ 
                    color: currentTheme.colors.primary,
                    fontFamily: currentTheme.fonts.heading,
                    fontSize: '18px',
                    fontWeight: 'bold',
                    marginBottom: '8px'
                  }}
                >
                  حذيفه عبدالمعز الحذيفي
                </h3>
                <p 
                  style={{ 
                    color: currentTheme.colors.secondary,
                    fontSize: '14px',
                    marginBottom: '12px'
                  }}
                >
                  مهندس تقنية معلومات
                </p>
                <p style={{ fontSize: '12px', lineHeight: '1.6' }}>
                  هذا نص تجريبي لمعاينة شكل القالب في ملف PDF النهائي.
                  يمكنك رؤية كيف ستبدو الألوان والخطوط في السيرة الذاتية.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة القوالب المتاحة */}
      <Card className="bg-gray-900/50 border-amber-500/20">
        <CardHeader>
          <CardTitle className="text-amber-400">القوالب المتاحة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {allThemes.map((theme) => {
              const isSelected = selectedTheme.name === theme.name;
              const isPreviewing = previewTheme?.name === theme.name;
              const accessibility = validateThemeAccessibility(theme);

              return (
                <div
                  key={theme.name}
                  className={cn(
                    'relative p-4 border rounded-lg cursor-pointer transition-all',
                    isSelected 
                      ? 'border-amber-500 bg-amber-500/10' 
                      : isPreviewing
                      ? 'border-blue-500 bg-blue-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                  )}
                >
                  {/* اسم القالب */}
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold">{theme.displayName}</h3>
                    {isSelected && (
                      <Check className="w-4 h-4 text-amber-500" />
                    )}
                  </div>

                  {/* معاينة الألوان */}
                  <div className="flex gap-1 mb-3">
                    {Object.values(getThemePreviewColors(theme)).map((color, index) => (
                      <div
                        key={index}
                        className="w-4 h-4 rounded-sm border border-gray-600"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>

                  {/* معلومات إضافية */}
                  <div className="flex items-center gap-2 mb-3">
                    <Badge 
                      variant={accessibility.isAccessible ? "default" : "secondary"}
                      className={cn(
                        "text-xs",
                        accessibility.isAccessible ? "bg-green-600" : "bg-yellow-600"
                      )}
                    >
                      {accessibility.isAccessible ? "ممتاز" : "جيد"}
                    </Badge>
                    <span className="text-xs text-gray-400">
                      {theme.fonts.primary}
                    </span>
                  </div>

                  {/* أزرار التحكم */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePreview(theme)}
                      className="flex-1 text-xs"
                    >
                      <Eye className="w-3 h-3 ml-1" />
                      معاينة
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleThemeSelect(theme)}
                      className="flex-1 text-xs bg-amber-600 hover:bg-amber-700"
                      disabled={isSelected}
                    >
                      {isSelected ? 'مُحدد' : 'اختيار'}
                    </Button>
                  </div>

                  {/* تحذيرات إمكانية الوصول */}
                  {!accessibility.isAccessible && (
                    <div className="mt-2 p-2 bg-yellow-500/10 border border-yellow-500/20 rounded text-xs">
                      <div className="flex items-center gap-1 text-yellow-400">
                        <Info className="w-3 h-3" />
                        <span>تحذير:</span>
                      </div>
                      <div className="text-yellow-300 mt-1">
                        {accessibility.issues[0]}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* أزرار إضافية */}
          <div className="flex gap-4 mt-6 pt-4 border-t border-gray-700">
            {previewTheme && (
              <Button
                variant="outline"
                onClick={clearPreview}
                className="border-gray-600 text-gray-300"
              >
                إلغاء المعاينة
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => onThemeChange(defaultTheme)}
              className="border-amber-500 text-amber-400"
            >
              العودة للقالب الافتراضي
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThemeSelector;
