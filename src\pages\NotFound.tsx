import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Home, ArrowLeft } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />

      <div className="pt-20 pb-12 min-h-screen flex items-center justify-center">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* 404 Animation */}
          <div className="mb-8">
            <h1 className="text-8xl lg:text-9xl font-bold text-amber-400 mb-4 animate-pulse">
              404
            </h1>
            <div className="w-32 h-1 bg-amber-400 mx-auto mb-8 rounded-full"></div>
          </div>

          {/* Error Message */}
          <div className="mb-8">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              الصفحة غير موجودة
            </h2>
            <p className="text-xl text-gray-300 mb-2">
              عذراً، لا يمكن العثور على الصفحة التي تبحث عنها
            </p>
            <p className="text-gray-400">
              المسار المطلوب: <code className="bg-gray-800 px-2 py-1 rounded text-amber-400">{location.pathname}</code>
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-8 py-3 rounded-full">
              <Link to="/">
                <Home className="w-4 h-4 ml-2" />
                العودة للرئيسية
              </Link>
            </Button>

            <Button
              variant="outline"
              onClick={() => window.history.back()}
              className="border-amber-500/40 text-amber-400 hover:bg-amber-500/10 px-8 py-3 rounded-full"
            >
              <ArrowLeft className="w-4 h-4 ml-2" />
              العودة للخلف
            </Button>
          </div>

          {/* Helpful Links */}
          <div className="mt-12 pt-8 border-t border-gray-700">
            <p className="text-gray-400 mb-4">أو يمكنك زيارة إحدى هذه الصفحات:</p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link to="/about" className="text-amber-400 hover:text-amber-300 transition-colors">
                السيرة الذاتية
              </Link>
              <Link to="/projects" className="text-amber-400 hover:text-amber-300 transition-colors">
                المشاريع
              </Link>
              <Link to="/tech-news" className="text-amber-400 hover:text-amber-300 transition-colors">
                الأخبار التقنية
              </Link>
              <Link to="/contact" className="text-amber-400 hover:text-amber-300 transition-colors">
                التواصل
              </Link>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default NotFound;
