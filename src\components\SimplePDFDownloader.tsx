import React, { useState } from 'react';
import { Download, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import jsPDF from 'jspdf';

const SimplePDFDownloader: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  const generatePDF = async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);
      
      toast.loading('جاري إنشاء ملف PDF...', {
        id: 'pdf-download',
        description: 'يتم تحضير السيرة الذاتية',
      });

      // انتظار قصير لإظهار التحميل
      await new Promise(resolve => setTimeout(resolve, 1000));

      // إنشاء PDF جديد
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // إعداد الخط والألوان
      doc.setFont("helvetica");
      
      // العنوان الرئيسي
      doc.setFontSize(22);
      doc.setTextColor(245, 158, 11); // لون ذهبي
      doc.text('Hodifa Abdulmaz Alhodaifi', 105, 25, { align: 'center' });
      
      doc.setFontSize(16);
      doc.setTextColor(100, 100, 100);
      doc.text('Information Technology Engineer', 105, 35, { align: 'center' });
      
      // خط فاصل
      doc.setDrawColor(245, 158, 11);
      doc.setLineWidth(1);
      doc.line(20, 42, 190, 42);
      
      // معلومات التواصل
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Contact Information', 20, 55);
      
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      let yPos = 65;
      
      doc.text('Email: <EMAIL>', 20, yPos);
      yPos += 8;
      doc.text('Phone: +967 777548421 / +967 718706242', 20, yPos);
      yPos += 8;
      doc.text('Location: Aden, Yemen', 20, yPos);
      yPos += 8;
      doc.text('Website: www.hodifatech.com', 20, yPos);
      yPos += 15;
      
      // الملخص المهني
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Professional Summary', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0);
      
      const summary = `Fourth-year Information Technology student at the Faculty of Engineering, University of Aden. 
I have modest experience in programming languages including C++, C#, HTML, CSS, JavaScript, PHP, and SQL. 
I have previously worked on developing simple Windows programs using C# and have now transitioned to web development, 
working on a nearly complete project using Laravel 11. Additionally, I have modest experience in database design 
and have worked with SQL Server and MySQL. I also have knowledge in software engineering and analysis, 
as well as basic knowledge of design patterns. I am eager to learn more and expand my practical experience.`;
      
      const summaryLines = doc.splitTextToSize(summary, 170);
      doc.text(summaryLines, 20, yPos);
      yPos += summaryLines.length * 5 + 15;
      
      // التعليم
      if (yPos > 250) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Education', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      
      doc.text('• University of Aden - Faculty of Engineering', 25, yPos);
      yPos += 7;
      doc.text('  Bachelor of Information Technology - Fourth Year', 25, yPos);
      yPos += 7;
      doc.text('  2021 - 2025', 25, yPos);
      yPos += 15;
      
      // الخبرة العملية
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Work Experience', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      
      doc.text('• Database Analysis Partner', 25, yPos);
      yPos += 7;
      doc.text('  Technical Institute - 2023', 25, yPos);
      yPos += 7;
      doc.text('  Worked as a partner in database analysis for a school in a Yemeni village', 25, yPos);
      yPos += 10;
      
      doc.text('• Freelance Trader', 25, yPos);
      yPos += 7;
      doc.text('  Self-employed - 2022 - Present', 25, yPos);
      yPos += 7;
      doc.text('  Working in freelance trading', 25, yPos);
      yPos += 15;
      
      // المشاريع
      if (yPos > 220) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Projects', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0);
      
      doc.text('• Booking Management Program', 25, yPos);
      yPos += 6;
      doc.text('  Technologies: C#, Windows Forms', 25, yPos);
      yPos += 6;
      doc.text('  Built a simple desktop program for booking operations', 25, yPos);
      yPos += 10;
      
      doc.text('• E-commerce Website - Laravel 11', 25, yPos);
      yPos += 6;
      doc.text('  Technologies: Laravel 11, PHP, MySQL', 25, yPos);
      yPos += 6;
      doc.text('  Built a commercial website specializing in newborn baby needs', 25, yPos);
      yPos += 10;
      
      doc.text('• School Database Design', 25, yPos);
      yPos += 6;
      doc.text('  Technologies: SQL Server, Database Design', 25, yPos);
      yPos += 6;
      doc.text('  Worked as a partner in analyzing and designing databases for schools', 25, yPos);
      yPos += 15;
      
      // المهارات
      if (yPos > 180) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Technical Skills', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0);
      
      doc.text('Frontend Development:', 25, yPos);
      doc.text('React 18, TypeScript, HTML5, CSS3, JavaScript ES6+, Tailwind CSS', 25, yPos + 6);
      yPos += 18;
      
      doc.text('Backend Development:', 25, yPos);
      doc.text('PHP, Laravel, C++, C#, Node.js', 25, yPos + 6);
      yPos += 18;
      
      doc.text('Database Management:', 25, yPos);
      doc.text('SQL Server, MySQL, SQL', 25, yPos + 6);
      yPos += 18;
      
      doc.text('Tools & Technologies:', 25, yPos);
      doc.text('Git, GitHub, Vite, ESLint, Figma', 25, yPos + 6);
      yPos += 18;
      
      // اللغات
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Languages', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      
      doc.text('• Arabic: Native', 25, yPos);
      yPos += 7;
      doc.text('• English: Excellent', 25, yPos);
      yPos += 7;
      doc.text('• French: Intermediate', 25, yPos);
      yPos += 15;
      
      // الدورات
      if (yPos > 200) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('Courses & Certifications', 20, yPos);
      
      yPos += 10;
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      
      const courses = [
        'Programming Fundamentals & C++ - Programming Advices Platform (2022-2024)',
        'Desktop Application Development - Programming Advices Platform (2024)',
        'Frontend Web Development - Alzero Web School',
        'Backend Web Development - Arab Generation Academy',
        'English Language - AMIDEAST American Institute (Full Year)',
        'CCNA - September 2023',
        'CPS - AMIDEAST American Institute'
      ];
      
      courses.forEach(course => {
        if (yPos > 270) {
          doc.addPage();
          yPos = 20;
        }
        doc.text(`• ${course}`, 25, yPos);
        yPos += 6;
      });
      
      // Footer
      yPos += 20;
      if (yPos > 270) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFontSize(8);
      doc.setTextColor(150, 150, 150);
      doc.text('Generated on: ' + new Date().toLocaleDateString(), 105, yPos, { align: 'center' });
      doc.text('© 2025 Hodifa Alhodaifi - All Rights Reserved', 105, yPos + 8, { align: 'center' });
      doc.text('www.hodifatech.com', 105, yPos + 16, { align: 'center' });
      
      // تحميل الملف
      const fileName = `Hodifa_Alhodaifi_CV_${new Date().toLocaleDateString('en-US').replace(/\//g, '-')}.pdf`;
      doc.save(fileName);

      toast.success('تم تحميل السيرة الذاتية بنجاح!', {
        id: 'pdf-download',
        description: `تم حفظ الملف: ${fileName}`,
        duration: 5000,
      });

    } catch (error) {
      console.error('خطأ في إنشاء PDF:', error);
      
      toast.error('فشل في تحميل السيرة الذاتية', {
        id: 'pdf-download',
        description: 'حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.',
        duration: 5000,
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <Button
        onClick={generatePDF}
        disabled={isGenerating}
        className={`
          w-full bg-gradient-to-r from-amber-500 to-amber-600 
          hover:from-amber-600 hover:to-amber-700 
          text-black font-bold text-lg px-8 py-4 
          rounded-xl shadow-lg hover:shadow-xl 
          transition-all duration-300 transform hover:scale-105
          disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          ${isGenerating ? 'animate-pulse cursor-wait' : ''}
        `}
      >
        {isGenerating ? (
          <>
            <Loader2 className="w-5 h-5 ml-2 animate-spin" />
            جاري إنشاء PDF...
          </>
        ) : (
          <>
            <Download className="w-5 h-5 ml-2" />
            تحميل السيرة الذاتية PDF
          </>
        )}
      </Button>

      <div className="text-center space-y-2">
        <div className="text-sm text-gray-400">
          ملف PDF احترافي • تحميل مضمون • متوافق مع جميع الأجهزة
        </div>
        
        {isGenerating && (
          <div className="w-full max-w-xs">
            <div className="bg-gray-200 rounded-full h-1.5 overflow-hidden">
              <div className="bg-amber-500 h-1.5 rounded-full animate-pulse w-full"></div>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              جاري معالجة البيانات وإنشاء PDF...
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimplePDFDownloader;
