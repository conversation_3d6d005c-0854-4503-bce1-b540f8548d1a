// تصدير جميع مكونات نظام PDF

// المكونات الأساسية
export { default as PDFGenerator } from '../PDFGenerator';
export { default as PDFPreview } from '../PDFPreview';
export { default as DownloadButton } from '../DownloadButton';
export { default as ThemeSelector } from '../ThemeSelector';

// أدوات الخطوط
export { 
  loadArabicFonts,
  arabicFontOptions,
  getDefaultArabicFont,
  checkFontLoading,
  loadFallbackFont,
  initializeArabicFonts,
  default as ArabicFontLoader
} from '../ArabicFontLoader';

// الـ Hooks
export { 
  usePDFGenerator,
  useCVData,
  default as usePDFGeneratorHook
} from '../../hooks/usePDFGenerator';

// الأنواع والواجهات
export type {
  PersonalInfo,
  Education,
  Experience,
  Project,
  Skills,
  Language,
  CVData,
  FontOption,
  ArabicFontOptions,
  PDFGeneratorProps,
  DownloadButtonProps,
  PDFPreviewProps,
  DownloadStatus,
  FontLoadingState
} from '../../types/pdf';

export {
  PDF_CONFIG,
  FONT_URLS
} from '../../types/pdf';

// القوالب والأنماط
export {
  allThemes,
  defaultTheme,
  professionalBlueTheme,
  naturalGreenTheme,
  creativePurpleTheme,
  boldRedTheme,
  classicGrayTheme,
  getThemeByName,
  createPDFStylesFromTheme,
  getThemePreviewColors,
  checkColorContrast,
  validateThemeAccessibility,
  type PDFTheme
} from '../../styles/pdfThemes';

// الأدوات المساعدة
export {
  formatDateForPDF,
  generatePDFFileName,
  sanitizeTextForPDF,
  splitTextForPDF,
  validateCVData,
  formatDataForPrint,
  estimatePageCount,
  generateTextPreview,
  exportCVDataAsJSON,
  importCVDataFromJSON,
  createDataBackup,
  default as pdfUtils
} from '../../utils/pdfUtils';

// ثوابت مفيدة
export const PDF_SYSTEM_VERSION = '1.0.0';

export const SUPPORTED_LANGUAGES = [
  'العربية',
  'الإنجليزية',
  'الفرنسية'
] as const;

export const SUPPORTED_FONTS = [
  'Amiri',
  'Cairo', 
  'Tajawal',
  'Lateef',
  'NotoSansArabic'
] as const;

export const DEFAULT_CV_SECTIONS = [
  'personalInfo',
  'summary',
  'education',
  'experience',
  'projects',
  'skills',
  'languages',
  'courses'
] as const;

// دوال مساعدة سريعة
export const createQuickPDF = async (data: CVData, theme?: PDFTheme) => {
  const { PDFGenerator } = await import('../PDFGenerator');
  // يمكن إضافة منطق إنشاء PDF سريع هنا
};

export const validatePDFSystem = () => {
  const checks = {
    fontsSupported: typeof window !== 'undefined' && 'fonts' in document,
    pdfLibraryLoaded: typeof window !== 'undefined',
    arabicSupport: /[\u0600-\u06FF]/.test('مرحبا'),
    rtlSupport: typeof window !== 'undefined' && 'dir' in document.documentElement,
  };

  return {
    isSupported: Object.values(checks).every(Boolean),
    checks
  };
};

// معلومات النظام
export const PDF_SYSTEM_INFO = {
  version: PDF_SYSTEM_VERSION,
  supportedFormats: ['PDF'],
  supportedLanguages: SUPPORTED_LANGUAGES,
  supportedFonts: SUPPORTED_FONTS,
  features: [
    'دعم كامل للغة العربية',
    'تنسيق RTL',
    'خطوط عربية متعددة',
    'قوالب ألوان متنوعة',
    'معاينة مباشرة',
    'تحميل تفاعلي',
    'التحقق من إمكانية الوصول',
    'تصدير واستيراد البيانات'
  ],
  requirements: [
    'متصفح حديث يدعم ES6+',
    'دعم Web Fonts',
    'JavaScript مُفعل'
  ]
};

export default {
  // المكونات
  PDFGenerator,
  PDFPreview,
  DownloadButton,
  ThemeSelector,
  
  // الـ Hooks
  usePDFGenerator,
  useCVData,
  
  // القوالب
  allThemes,
  defaultTheme,
  getThemeByName,
  
  // الأدوات
  pdfUtils,
  ArabicFontLoader,
  
  // المعلومات
  PDF_SYSTEM_INFO,
  validatePDFSystem,
};
