import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  console.log('Build mode:', mode);
  console.log('VITE_BASE_URL:', process.env.VITE_BASE_URL);

  return {
  // إعداد base للدومين المخصص
  base: '/',
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // تحسين البناء للإنتاج
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          query: ['@tanstack/react-query'],
        },
      },
    },
    // تحسين الضغط
    reportCompressedSize: false,
    chunkSizeWarningLimit: 1000,
  },
};
});
