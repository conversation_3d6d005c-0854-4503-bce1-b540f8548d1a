import React, { useState } from 'react';
import { <PERSON>, EyeOff, Download, FileText, Maximize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import PDFGenerator from './PDFGenerator';

interface PDFPreviewProps {
  className?: string;
}

const PDFPreview: React.FC<PDFPreviewProps> = ({ className = '' }) => {
  const [showPreview, setShowPreview] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const previewContent = (
    <div className="bg-white text-black p-8 rounded-lg shadow-lg max-w-4xl mx-auto" dir="rtl">
      {/* Header */}
      <div className="text-center border-b-2 border-amber-500 pb-6 mb-8">
        <h1 className="text-3xl font-bold text-amber-600 mb-2">
          حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
        </h1>
        <p className="text-xl text-gray-600 mb-4">مهندس تقنية معلومات</p>
        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
          <span>📧 <EMAIL></span>
          <span>📱 +967 777548421</span>
          <span>📍 عدن، اليمن</span>
          <span>🌐 www.hodifatech.com</span>
        </div>
      </div>

      {/* Professional Summary */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-amber-600 mb-4 border-b border-gray-300 pb-2">
          الملخص المهني
        </h2>
        <p className="text-gray-700 leading-relaxed">
          طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: 
          C++, C#, Html, Css, Js, Php, Sql كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب...
        </p>
      </div>

      {/* Education */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-amber-600 mb-4 border-b border-gray-300 pb-2">
          التعليم
        </h2>
        <div className="border-r-4 border-amber-500 pr-4">
          <h3 className="font-bold text-gray-800">جامعة عدن - كلية الهندسة</h3>
          <p className="text-gray-600">بكالوريوس تقنية المعلومات - السنة الرابعة</p>
          <p className="text-sm text-gray-500">2021 - 2025</p>
        </div>
      </div>

      {/* Experience */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-amber-600 mb-4 border-b border-gray-300 pb-2">
          الخبرة العملية
        </h2>
        <div className="space-y-4">
          <div className="border-r-4 border-amber-500 pr-4">
            <h3 className="font-bold text-gray-800">شريك في تحليل قواعد البيانات</h3>
            <p className="text-sm text-gray-500">معهد تقني - 2023</p>
            <p className="text-gray-600">عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية</p>
          </div>
          <div className="border-r-4 border-amber-500 pr-4">
            <h3 className="font-bold text-gray-800">تاجر حر</h3>
            <p className="text-sm text-gray-500">العمل الحر - 2022 - الحاضر</p>
            <p className="text-gray-600">أعمل في مجال التجارة الحرة</p>
          </div>
        </div>
      </div>

      {/* Skills Preview */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-amber-600 mb-4 border-b border-gray-300 pb-2">
          المهارات التقنية
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-bold text-gray-800 mb-2">تطوير الواجهات الأمامية</h3>
            <div className="flex flex-wrap gap-2">
              {['React 18', 'TypeScript', 'HTML5', 'CSS3', 'Tailwind CSS'].map((skill, index) => (
                <span key={index} className="bg-amber-100 text-amber-800 px-2 py-1 rounded text-xs">
                  {skill}
                </span>
              ))}
            </div>
          </div>
          <div>
            <h3 className="font-bold text-gray-800 mb-2">تطوير الخلفية</h3>
            <div className="flex flex-wrap gap-2">
              {['PHP', 'Laravel', 'C++', 'C#', 'Node.js'].map((skill, index) => (
                <span key={index} className="bg-amber-100 text-amber-800 px-2 py-1 rounded text-xs">
                  {skill}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Languages */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-amber-600 mb-4 border-b border-gray-300 pb-2">
          اللغات
        </h2>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>العربية</span>
            <span className="text-amber-600">الأم</span>
          </div>
          <div className="flex justify-between">
            <span>الإنجليزية</span>
            <span className="text-amber-600">ممتاز</span>
          </div>
          <div className="flex justify-between">
            <span>الفرنسية</span>
            <span className="text-amber-600">متوسط</span>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-400 border-t border-gray-300 pt-4">
        <p>تم إنشاء هذه السيرة الذاتية بواسطة نظام PDF احترافي يدعم اللغة العربية</p>
        <p>© 2025 حذيفه الحذيفي - جميع الحقوق محفوظة</p>
      </div>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* أزرار التحكم */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
        {/* زر التحميل الرئيسي */}
        <div className="w-full sm:w-auto">
          <PDFGenerator />
        </div>

        {/* زر المعاينة */}
        <Button
          onClick={() => setShowPreview(!showPreview)}
          variant="outline"
          className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-black"
        >
          {showPreview ? (
            <>
              <EyeOff className="w-4 h-4 ml-2" />
              إخفاء المعاينة
            </>
          ) : (
            <>
              <Eye className="w-4 h-4 ml-2" />
              معاينة السيرة الذاتية
            </>
          )}
        </Button>

        {/* زر المعاينة بملء الشاشة */}
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="border-gray-500 text-gray-400 hover:bg-gray-500 hover:text-white"
            >
              <Maximize2 className="w-4 h-4 ml-2" />
              معاينة كاملة
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-center text-amber-600">
                معاينة السيرة الذاتية
              </DialogTitle>
            </DialogHeader>
            <div className="mt-4">
              {previewContent}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* المعاينة المدمجة */}
      {showPreview && (
        <Card className="bg-gray-50 border-amber-500/20">
          <CardHeader>
            <CardTitle className="text-center text-amber-600 flex items-center justify-center gap-2">
              <FileText className="w-5 h-5" />
              معاينة السيرة الذاتية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="max-h-96 overflow-y-auto">
              {previewContent}
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-500 mb-4">
                هذه معاينة مبسطة. الملف الفعلي سيحتوي على تفاصيل أكثر وتنسيق أفضل.
              </p>
              <PDFGenerator className="max-w-md mx-auto" />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PDFPreview;
