// نظام مراقبة الدردشة والأداء

interface ChatMetrics {
  totalMessages: number;
  activeUsers: number;
  messagesPerMinute: number;
  lastActivity: number;
}

class ChatMonitoring {
  private metrics: ChatMetrics = {
    totalMessages: 0,
    activeUsers: 0,
    messagesPerMinute: 0,
    lastActivity: Date.now()
  };

  private messageTimestamps: number[] = [];

  // تسجيل رسالة جديدة
  logMessage(userId: string) {
    const now = Date.now();
    this.metrics.totalMessages++;
    this.metrics.lastActivity = now;
    this.messageTimestamps.push(now);

    // الاحتفاظ بالرسائل من آخر دقيقة فقط
    this.messageTimestamps = this.messageTimestamps.filter(
      timestamp => now - timestamp <= 60000
    );

    this.metrics.messagesPerMinute = this.messageTimestamps.length;

    // إرسال التحليلات إذا كان متاح
    this.sendAnalytics('message_sent', {
      userId,
      timestamp: now,
      totalMessages: this.metrics.totalMessages
    });
  }

  // تسجيل مستخدم نشط
  logActiveUser(userId: string) {
    this.sendAnalytics('user_active', {
      userId,
      timestamp: Date.now()
    });
  }

  // تسجيل خطأ
  logError(error: string, context?: any) {
    console.error('Chat Error:', error, context);
    this.sendAnalytics('chat_error', {
      error,
      context,
      timestamp: Date.now()
    });
  }

  // الحصول على المقاييس الحالية
  getMetrics(): ChatMetrics {
    return { ...this.metrics };
  }

  // إرسال التحليلات (يمكن ربطه بـ Google Analytics أو Firebase Analytics)
  private sendAnalytics(eventName: string, data: any) {
    try {
      // إرسال إلى Google Analytics إذا كان متاح
      if (typeof gtag !== 'undefined') {
        gtag('event', eventName, {
          custom_parameter_1: data.userId,
          custom_parameter_2: data.timestamp,
          ...data
        });
      }

      // إرسال إلى Firebase Analytics إذا كان متاح
      if (typeof firebase !== 'undefined' && firebase.analytics) {
        firebase.analytics().logEvent(eventName, data);
      }

      // حفظ محلي للتطوير
      if (import.meta.env.DEV) {
        console.log('Analytics Event:', eventName, data);
      }
    } catch (error) {
      console.warn('Failed to send analytics:', error);
    }
  }

  // تنظيف البيانات القديمة
  cleanup() {
    const now = Date.now();
    this.messageTimestamps = this.messageTimestamps.filter(
      timestamp => now - timestamp <= 60000
    );
  }

  // فحص الحدود والتحذيرات
  checkLimits(): { warning?: string; error?: string } {
    const metrics = this.getMetrics();
    
    // تحذير إذا كان هناك نشاط مكثف
    if (metrics.messagesPerMinute > 50) {
      return {
        warning: 'نشاط مكثف في الدردشة - قد يؤثر على الأداء'
      };
    }

    // خطأ إذا تم تجاوز الحدود
    if (metrics.messagesPerMinute > 100) {
      return {
        error: 'تم تجاوز الحد الأقصى للرسائل - يرجى الانتظار'
      };
    }

    return {};
  }
}

// إنشاء instance واحد للتطبيق
export const chatMonitoring = new ChatMonitoring();

// تنظيف دوري كل دقيقة
setInterval(() => {
  chatMonitoring.cleanup();
}, 60000);

export default ChatMonitoring;
